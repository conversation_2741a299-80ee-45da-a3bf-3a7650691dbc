
import pymupdf4llm
import pymupdf
import json
import logging
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
import re
from pathlib import Path
import time
from concurrent.futures import ProcessPoolExecutor, as_completed

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('pdf_converter.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def print_dict(str):
    """格式化打印字典"""
    print(json.dumps(str, ensure_ascii=False, indent=2))


@dataclass
class TitleInfo:
    """标题信息"""
    level: int
    text: str
    page_num: int
    original_text: str  # TOC中的原始文本

    
def clean_title_text(title: str) -> str:
    """清理标题文本"""
    # 移除多余的空格和特殊字符
    title = re.sub(r'\s+', ' ', title.strip())
        
    # 移除可能的页码信息
    title = re.sub(r'\s*\.\.\.\s*\d+\s*$', '', title)
    title = re.sub(r'\s*\d+\s*$', '', title)
        
    return title.strip()
    
def extract_toc_titles(doc: pymupdf.Document) -> List[TitleInfo]:
    """提取TOC标题信息"""
    logger.info("正在提取TOC标题信息...")
    
    toc = doc.get_toc()
    if not toc:
        logger.warning("PDF中没有找到目录信息")
        return []
    
    titles = []
    for item in toc:
        level, title, page_num = item
        # print(item)
        
        # 清理标题文本
        title_info = TitleInfo(
            level=level,
            text=clean_title_text(title),
            page_num=page_num,
            original_text=title
        )
        titles.append(title_info)
    
    logger.info(f"提取到 {len(titles)} 个标题")
    
    return titles

def extract_text_between_substrings(main_string_list: List[str], start_substring: str, end_substring: str) -> str:
    """
    在行列表中提取两个子字符串之间的内容。
    注意: 这里的匹配逻辑是根据用户的原始代码实现的，用于处理标题行。
    """
    def _normalize_for_match(text: str) -> str:
        """规范化文本，移除 Markdown 标记和多余空格以便匹配"""
        return re.sub(r'[*#\s]+', '', text)
    
    # 提取所有带有 # 或 ** 的行，并进行规范化
    hash_lines = []
    for i, line in enumerate(main_string_list):
        if '#' in line or '**' in line:
            hash_lines.append((i, _normalize_for_match(line)))

    start_title = _normalize_for_match(start_substring)
    end_title = _normalize_for_match(end_substring)
    start_index = -1
    end_index = -1
    
    # 精准匹配
    for i, normalized_line in hash_lines: 
        if start_title == normalized_line:
            start_index = i
        if end_title == normalized_line:
            end_index = i

    # 模糊匹配
    if start_index == -1:
        for i, normalized_line in hash_lines:
            if start_title in normalized_line:
                start_index = i
                break
    if end_index == -1:
        for i, normalized_line in hash_lines:
            if end_title in normalized_line:
                end_index = i
                break
            
                
    if start_index == -1 or end_index==-1:
        logger.warning(f"无法在文档中找到开始标题: '{start_substring}'")
        print(start_index)
        print(end_index)
        return None
    
    
    result_content = main_string_list[start_index+1 : end_index]
    
    if start_index  == end_index:
        # 如果找不到结束标题或开始和结束标题相同，则提取到文档末尾
        end_index = len(main_string_list)-1
        result_content = main_string_list[start_index:]
        
    #将头标题，尾标题，main_string_list，和最终content_content写入一个文件中，命名为chunk_标题
    with open(f'chunk_{start_substring.replace("/","_")}.txt', 'w', encoding='utf-8') as f:
        f.write(f"头标题: {start_substring}\n")
        f.write(f"尾标题: {end_substring}\n")
        f.write(f"内容: {"".join(main_string_list)}\n")
        f.write(f"最终截取: {('\n'.join(result_content))}\n")
        
    return re.sub(r"\n-+\n","\n",("\n".join(result_content)).replace('#', '//#'))
    




def extract_content_optimized(file_url: str) -> str:
    """
    优化后的内容提取函数。
    一次性读取整个PDF并转换为Markdown，然后通过处理内存中的文本列表来提取内容。
    """
    logger.info(f"开始处理文件: {file_url}")
    
    # 步骤 1: 一次性读取整个PDF并转换为Markdown
    doc = pymupdf.open(file_url)
    
    # 获取完整的Markdown内容字符串
    markdown_chunks_list = pymupdf4llm.to_markdown(doc,page_chunks=True,show_progress=True)

    result_content_list = []
    toc_list = []
    for markdown_chunk in markdown_chunks_list:
        toc_list.extend(markdown_chunk['toc_items'])
   
    # print(toc_list)
    for index,toc_item in enumerate(toc_list):
        
        start_title=toc_item
        all_lines=[]
        # 确定下一个标题作为结束标记
        if index < len(toc_list) - 1:
            end_title = toc_list[index + 1]
            end_index=end_title[2]
            for md_chunk in markdown_chunks_list[index:end_index+1]:
                all_lines.extend(md_chunk['text'].split('\n'))
        else:
            # 最后一个标题，没有结束标记
            end_title = start_title
            end_index=len(markdown_chunks_list)-1
            for md_chunk in markdown_chunks_list[index:]:
                all_lines.extend(md_chunk['text'].split('\n'))
              
        content = extract_text_between_substrings(all_lines, start_title[1], end_title[1])
        
        if content is not None:
            # 如果成功提取到内容，则格式化并添加到结果列表
            markdown_heading = "#" * int(toc_item[0])
            result_content_list.append(f"{markdown_heading} {toc_item[1]}\n{content}")
        else:
            logger.warning(f"无法为标题 '{toc_item[1]}' 提取到内容。")
            
    return '\n'.join(result_content_list)

def process_single_pdf(pdf_file: Path):
    """
    处理单个PDF文件的函数，用于并行执行。
    """
    try:
        markdown = extract_content_optimized(str(pdf_file))
        OUT_DIR = pdf_file.parent / 'md'
        OUT_DIR.mkdir(exist_ok=True)
        md_path = OUT_DIR / f'{pdf_file.stem}.md'
        
        # 写入 Markdown 文件
        md_path.write_text(markdown, encoding='utf-8')
        logger.info(f'Markdown 文件已成功保存到: {md_path}')
        return f'成功处理文件: {pdf_file.name}'
    except Exception as e:
        logger.error(f"处理文件 {pdf_file.name} 时出错: {e}")
        return f'处理文件 {pdf_file.name} 失败'
    
    
def convert_pdf_to_markdown(pdf_file: Path) -> str:
    if(pdf_file.exists()):
        #判断是否可以获取到toc
        doc = pymupdf.open(str(pdf_file))
        toc = doc.get_toc()
        if not toc:
            raise ValueError(f"PDF中没有找到目录信息: {pdf_file}")
        markdown = extract_content_optimized(str(pdf_file))
        return markdown
    else:
        raise FileNotFoundError(f"文件不存在: {pdf_file}")   

if __name__ == '__main__':


    start_time = time.time()
    markdown = extract_content_optimized('./raw_pdf/NE40E V800R023C10SPC500 配置指南 IP路由.pdf')  # 调用优化后的函数
    md_path = Path('b.md')
    md_path.write_text(markdown, encoding='utf-8')
    end_time = time.time()
    logger.info(f'转换完成，耗时: {end_time - start_time:.2f} 秒')