[project]
name = "log-llm-data-loader"
version = "0.1.0"
description = "日志LLM数据加载器 - 支持PDF、HTML、Markdown文件解析和文档树生成"
readme = "README.md"
requires-python = ">=3.11,<3.13"
dependencies = [
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "python-multipart>=0.0.6",
    "aiofiles>=23.2.0",
    "requests>=2.31.0",
    "pypandoc>=1.13",
    "markdown>=3.5.0",
    "pydantic>=2.5.0",
    "mineru[core]>=2.1.11",
    "html2text>=2025.4.15",
    "torch==2.8.0+cu126",
    "torchvision==0.23.0+cu126",
    "markitdown[all]>=0.1.3",
    "pymupdf4llm>=0.0.27",
    "pymupdf>=1.26.4",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src"]



[tool.uv]
dev-dependencies = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "httpx>=0.25.0",
]
index-strategy = "unsafe-best-match"
index-url = "https://pypi.org/simple/"
extra-index-url = ["https://download.pytorch.org/whl/cu126"]



