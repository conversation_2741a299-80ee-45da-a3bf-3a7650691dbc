<!doctype html><html lang=en dir=ltr class="docs-wrapper plugin-docs plugin-id-default docs-version-current docs-doc-page docs-doc-id-features/reasoning_outputs" data-has-hydrated=false><meta charset=UTF-8><meta name=generator content="Docusaurus v3.7.0"><title data-rh=true>推理输出 | vLLM 中文站</title><meta data-rh=true name=viewport content="width=device-width, initial-scale=1.0"><meta data-rh=true name=twitter:card content=summary_large_image><meta data-rh=true property=og:image content=https://vllm.hyper.ai/img/docusaurus-social-card.jpg><meta data-rh=true name=twitter:image content=https://vllm.hyper.ai/img/docusaurus-social-card.jpg><meta data-rh=true property=og:url content=https://vllm.hyper.ai/docs/features/reasoning_outputs><meta data-rh=true property=og:locale content=en><meta data-rh=true name=docusaurus_locale content=en><meta data-rh=true name=docsearch:language content=en><meta data-rh=true name=docusaurus_version content=current><meta data-rh=true name=docusaurus_tag content=docs-default-current><meta data-rh=true name=docsearch:version content=current><meta data-rh=true name=docsearch:docusaurus_tag content=docs-default-current><meta data-rh=true property=og:title content="推理输出 | vLLM 中文站"><meta data-rh=true name=description content="\*在线运行 vLLM 入门教程：零基础分步指南"><meta data-rh=true property=og:description content="\*在线运行 vLLM 入门教程：零基础分步指南"><link data-rh=true rel=icon href=/img/favicon.ico><link data-rh=true rel=canonical href=https://vllm.hyper.ai/docs/features/reasoning_outputs><link data-rh=true rel=alternate href=https://vllm.hyper.ai/docs/features/reasoning_outputs hreflang=en><link data-rh=true rel=alternate href=https://vllm.hyper.ai/docs/features/reasoning_outputs hreflang=x-default><link rel=preconnect href=https://www.google-analytics.com><link rel=preconnect href=https://www.googletagmanager.com><script async src="https://www.googletagmanager.com/gtag/js?id=G-YY2E0ZQRP8"></script><script>function gtag(){dataLayer.push(arguments)}window.dataLayer=window.dataLayer||[],gtag("js",new Date),gtag("config","G-YY2E0ZQRP8",{})</script><link rel=stylesheet href=https://workers.vrp.moe/api/jsd/katex@0.13.24/dist/katex.min.css type=text/css integrity=sha384-odtC+0UGzzFL/6PNoE8rX/SPcQDXBJ+uRepguP4QkPCm2LBxH3FA3y+fKSiJ+AmM crossorigin=anonymous><script src=https://get.openbayes.net/js/script.js defer data-domain=vllm.hyper.ai></script><link rel=stylesheet href=/assets/css/styles.e695fa89.css><script src=/assets/js/runtime~main.481a6cc7.js defer></script><script src=/assets/js/main.7e3cc06d.js defer></script><body class=navigation-with-keyboard><script>!function(){var t,e=function(){try{return new URLSearchParams(window.location.search).get("docusaurus-theme")}catch(t){}}()||function(){try{return window.localStorage.getItem("theme")}catch(t){}}();t=null!==e?e:"light",document.documentElement.setAttribute("data-theme",t)}(),function(){try{for(var[t,e]of new URLSearchParams(window.location.search).entries())if(t.startsWith("docusaurus-data-")){var a=t.replace("docusaurus-data-","data-");document.documentElement.setAttribute(a,e)}}catch(t){}}()</script><div id=__docusaurus><link rel=preload as=image href=/img/vllm-logo.png><div role=region aria-label="Skip to main content"><a class=skipToContent_RKkG href=#__docusaurus_skipToContent_fallback>Skip to main content</a></div><nav aria-label=Main class="navbar navbar--fixed-top"><div class=navbar__inner><div class=navbar__items><button aria-label="Toggle navigation bar" aria-expanded=false class="navbar__toggle clean-btn" type=button><svg width=30 height=30 viewBox="0 0 30 30" aria-hidden=true><path stroke=currentColor stroke-linecap=round stroke-miterlimit=10 stroke-width=2 d="M4 7h22M4 15h22M4 23h22"/></svg></button><a class=navbar__brand href=/><div class=navbar__logo><img src=/img/vllm-logo.png alt=vLLM class="themedComponent_KsVg themedComponent--light_e70H"><img src=/img/vllm-logo.png alt=vLLM class="themedComponent_KsVg themedComponent--dark_ufWO"></div><b class="navbar__title text--truncate">vLLM 中文站</b></a><a aria-current=page class="navbar__item navbar__link navbar__link--active" href=/docs/>查看文档</a><a class="navbar__item navbar__link" href=/about>关于</a><a href=https://github.com/hyperai/vllm-cn target=_blank rel="noopener noreferrer" class="navbar__item navbar__link">GitHub<svg width=13.5 height=13.5 aria-hidden=true viewBox="0 0 24 24" class=iconExternalLink_gHMw><path fill=currentColor d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"/></svg></a><a href=https://hyper.ai target=_blank rel="noopener noreferrer" class="navbar__item navbar__link">返回超神经<svg width=13.5 height=13.5 aria-hidden=true viewBox="0 0 24 24" class=iconExternalLink_gHMw><path fill=currentColor d="M21 13v10h-21v-19h12v2h-10v15h17v-8h2zm3-12h-10.988l4.035 4-6.977 7.07 2.828 2.828 6.977-7.07 4.125 4.172v-11z"/></svg></a></div><div class="navbar__items navbar__items--right"><div class="navbar__item dropdown dropdown--hoverable dropdown--right"><a class=navbar__link aria-haspopup=true aria-expanded=false role=button href=/docs/features/reasoning_outputs>Next</a><ul class=dropdown__menu><li><a aria-current=page class="dropdown__link dropdown__link--active" href=/docs/features/reasoning_outputs>Next</a><li><a class=dropdown__link href=/docs/0.8.x/>0.8.x</a></ul></div><div class="toggle_XAFf colorModeToggle_hZWI"><button class="clean-btn toggleButton_JW0i toggleButtonDisabled_QL2u" type=button disabled title="Switch between dark and light mode (currently light mode)" aria-label="Switch between dark and light mode (currently light mode)" aria-live=polite aria-pressed=false><svg viewBox="0 0 24 24" width=24 height=24 class=lightToggleIcon_TIWo><path fill=currentColor d="M12,9c1.65,0,3,1.35,3,3s-1.35,3-3,3s-3-1.35-3-3S10.35,9,12,9 M12,7c-2.76,0-5,2.24-5,5s2.24,5,5,5s5-2.24,5-5 S14.76,7,12,7L12,7z M2,13l2,0c0.55,0,1-0.45,1-1s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S1.45,13,2,13z M20,13l2,0c0.55,0,1-0.45,1-1 s-0.45-1-1-1l-2,0c-0.55,0-1,0.45-1,1S19.45,13,20,13z M11,2v2c0,0.55,0.45,1,1,1s1-0.45,1-1V2c0-0.55-0.45-1-1-1S11,1.45,11,2z M11,20v2c0,0.55,0.45,1,1,1s1-0.45,1-1v-2c0-0.55-0.45-1-1-1C11.45,19,11,19.45,11,20z M5.99,4.58c-0.39-0.39-1.03-0.39-1.41,0 c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0s0.39-1.03,0-1.41L5.99,4.58z M18.36,16.95 c-0.39-0.39-1.03-0.39-1.41,0c-0.39,0.39-0.39,1.03,0,1.41l1.06,1.06c0.39,0.39,1.03,0.39,1.41,0c0.39-0.39,0.39-1.03,0-1.41 L18.36,16.95z M19.42,5.99c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06c-0.39,0.39-0.39,1.03,0,1.41 s1.03,0.39,1.41,0L19.42,5.99z M7.05,18.36c0.39-0.39,0.39-1.03,0-1.41c-0.39-0.39-1.03-0.39-1.41,0l-1.06,1.06 c-0.39,0.39-0.39,1.03,0,1.41s1.03,0.39,1.41,0L7.05,18.36z"/></svg><svg viewBox="0 0 24 24" width=24 height=24 class=darkToggleIcon_x8z6><path fill=currentColor d="M9.37,5.51C9.19,6.15,9.1,6.82,9.1,7.5c0,4.08,3.32,7.4,7.4,7.4c0.68,0,1.35-0.09,1.99-0.27C17.45,17.19,14.93,19,12,19 c-3.86,0-7-3.14-7-7C5,9.07,6.81,6.55,9.37,5.51z M12,3c-4.97,0-9,4.03-9,9s4.03,9,9,9s9-4.03,9-9c0-0.46-0.04-0.92-0.1-1.36 c-0.98,1.37-2.58,2.26-4.4,2.26c-2.98,0-5.4-2.42-5.4-5.4c0-1.81,0.89-3.42,2.26-4.4C12.92,3.04,12.46,3,12,3L12,3z"/></svg></button></div><div class=navbarSearchContainer_axbW></div></div></div><div role=presentation class=navbar-sidebar__backdrop></div></nav><div id=__docusaurus_skipToContent_fallback class="main-wrapper mainWrapper_twcF"><div class=docsWrapper_UTyj><button aria-label="Scroll back to top" class="clean-btn theme-back-to-top-button backToTopButton_Em5M" type=button></button><div class=docRoot_kA7V><aside class="theme-doc-sidebar-container docSidebarContainer_gRLv"><div class=sidebarViewport_FqKF><div class=sidebar_LUUw><nav aria-label="Docs sidebar" class="menu thin-scrollbar menu_KCHs"><ul class="theme-doc-sidebar-menu menu__list"><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-1 menu__list-item"><a class=menu__link href=/docs/>欢迎来到 vLLM！</a><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/getting-started/installation/>快速开始</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/models/supported_models>支持模型</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret menu__link--active" role=button aria-expanded=true href=/docs/features/quantization/>功能特性</a></div><ul style=display:block;overflow:visible;height:auto class=menu__list><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-2 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist" tabindex=0 href=/docs/features/quantization/>量化</a><button aria-label="Expand sidebar category '量化'" aria-expanded=false type=button class="clean-btn menu__caret"></button></div><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class=menu__link tabindex=0 href=/docs/features/lora>LoRA 适配器</a><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class=menu__link tabindex=0 href=/docs/features/tool_calling>工具调用</a><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class="menu__link menu__link--active" aria-current=page tabindex=0 href=/docs/features/reasoning_outputs>推理输出</a><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class=menu__link tabindex=0 href=/docs/features/structured_outputs>结构化输出</a><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class=menu__link tabindex=0 href=/docs/features/automatic_prefix_caching>自动前缀缓存</a><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class=menu__link tabindex=0 href=/docs/features/disagg_prefill>分离式预填充（实验性功能）</a><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class=menu__link tabindex=0 href=/docs/features/spec_decode>分离式预填充（实验性功能）</a><li class="theme-doc-sidebar-item-link theme-doc-sidebar-item-link-level-2 menu__list-item"><a class=menu__link tabindex=0 href=/docs/features/compatibility_matrix>兼容矩阵</a></ul><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/training/trl>训练</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/inference-and-serving/offline_inference>推理</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/deployment/docker>部署</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/performance/optimization>性能</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/design/arch_overview>设计文档</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/design-v1/torch_compile>V1 设计文档</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/contributing/overview>开发者指南</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/api/offline_interence/>API</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/community/blog>社区</a></div><li class="theme-doc-sidebar-item-category theme-doc-sidebar-item-category-level-1 menu__list-item menu__list-item--collapsed"><div class=menu__list-item-collapsible><a class="menu__link menu__link--sublist menu__link--sublist-caret" role=button aria-expanded=false href=/docs/vllm-tutorials/vLLM-stepbysteb>vLLM 教程</a></div></ul></nav></div></div></aside><main class=docMainContainer_sD0o><div class="container padding-top--md padding-bottom--lg"><div class=row><div class="col docItemCol_TipQ"><div class=docItemContainer_eqE9><article><nav class="theme-doc-breadcrumbs breadcrumbsContainer_pLZt" aria-label=Breadcrumbs><ul class=breadcrumbs itemscope itemtype=https://schema.org/BreadcrumbList><li class=breadcrumbs__item><a aria-label="Home page" class=breadcrumbs__link href=/><svg viewBox="0 0 24 24" class=breadcrumbHomeIcon_cVZV><path d="M10 19v-5h4v5c0 .55.45 1 1 1h3c.55 0 1-.45 1-1v-7h1.7c.46 0 .68-.57.33-.87L12.67 3.6c-.38-.34-.96-.34-1.34 0l-8.36 7.53c-.34.3-.13.87.33.87H5v7c0 .55.45 1 1 1h3c.55 0 1-.45 1-1z" fill=currentColor /></svg></a><li class=breadcrumbs__item><span class=breadcrumbs__link>功能特性</span><meta itemprop=position content=1><li itemscope itemprop=itemListElement itemtype=https://schema.org/ListItem class="breadcrumbs__item breadcrumbs__item--active"><span class=breadcrumbs__link itemprop=name>推理输出</span><meta itemprop=position content=2></ul></nav><span class="theme-doc-version-badge badge badge--secondary">Version: Next</span><div class="tocCollapsible_WU86 theme-doc-toc-mobile tocMobile_Vizd"><button type=button class="clean-btn tocCollapsibleButton_WfIa">On this page</button></div><div class="theme-doc-markdown markdown"><header><h1>推理输出</h1></header><p><a href="https://openbayes.com/console/public/tutorials/rXxb5fZFr29?utm_source=vLLM-CNdoc&utm_medium=vLLM-CNdoc-V1&utm_campaign=vLLM-CNdoc-V1-25ap" target=_blank rel="noopener noreferrer">*在线运行 vLLM 入门教程：零基础分步指南</a></p>
<p>vLLM 支持推理模型，例如 <a href=https://huggingface.co/deepseek-ai/DeepSeek-R1 target=_blank rel="noopener noreferrer">DeepSeek R1</a>，这些模型旨在生成包含推理步骤和最终结论的输出。</p>
<p>推理模型在其输出中返回一个额外的 <code>reasoning_content</code> 字段，该字段包含导致最终结论的推理步骤。其他模型的输出中不存在此字段。</p>
<h2 class="anchor anchorWithStickyNavbar_Ntt5" id=支持的模型>支持的模型<a href=#支持的模型 class=hash-link aria-label="Direct link to 支持的模型" title="Direct link to 支持的模型">​</a></h2>
<p>vLLM 目前支持以下推理模型：</p>























<table><thead><tr><th>型号系列<th>解析器名称<th>结构化输出支持<th>工具调用<tbody><tr><td><a href=https://huggingface.co/collections/deepseek-ai/deepseek-r1-678e1e131c0169c0bc89728d target=_blank rel="noopener noreferrer">DeepSeek R1 series  DeepSeek R1 系列</a><td>deepseek_r1<td>guided_json, guided_regex<td>guided_json、guided_regex<tr><td><a href=https://huggingface.co/Qwen/QwQ-32B target=_blank rel="noopener noreferrer">QwQ-32B</a><td>deepseek_r1<td>guided_json, guided_regex<td>guided_json、guided_regex</table>
<h2 class="anchor anchorWithStickyNavbar_Ntt5" id=快速入门>快速入门<a href=#快速入门 class=hash-link aria-label="Direct link to 快速入门" title="Direct link to 快速入门">​</a></h2>
<p>要使用推理模型，您需要在向聊天补全端点发出请求时指定 <code>--enable-reasoning</code> 和 <code>--reasoning-parser</code> 标志。<code>--reasoning-parser</code> 标志指定用于从模型输出中提取推理内容的推理解析器。</p>
<div class="language-plain codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-plain codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token plain">vllm serve deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B \</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    --enable-reasoning --reasoning-parser deepseek_r1</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<p>接下来，向模型发出请求，该请求应在响应中返回推理内容。</p>
<div class="language-plain codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-plain codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token plain">from openai import OpenAI</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"># 修改 OpenAI 的 API 密钥和 API 基础 URL 以使用 vLLM 的 API 服务器。</span><br></span><span class=token-line style=color:#393A34><span class="token plain">openai_api_key = "EMPTY"</span><br></span><span class=token-line style=color:#393A34><span class="token plain">openai_api_base = "http://localhost:8000/v1"</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">client = OpenAI(</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    api_key=openai_api_key,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    base_url=openai_api_base,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">)</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">models = client.models.list()</span><br></span><span class=token-line style=color:#393A34><span class="token plain">model = models.data[0].id</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"># 第一轮</span><br></span><span class=token-line style=color:#393A34><span class="token plain">messages = [{"role": "user", "content": "9.11 and 9.8, which is greater?"}]</span><br></span><span class=token-line style=color:#393A34><span class="token plain">response = client.chat.completions.create(model=model, messages=messages)</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">reasoning_content = response.choices[0].message.reasoning_content</span><br></span><span class=token-line style=color:#393A34><span class="token plain">content = response.choices[0].message.content</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">print("reasoning_content:", reasoning_content)</span><br></span><span class=token-line style=color:#393A34><span class="token plain">print("content:", content)</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<p><code>reasoning_content</code> 字段包含导致最终结论的推理步骤，而 <code>content</code> 字段包含最终结论。</p>
<h2 class="anchor anchorWithStickyNavbar_Ntt5" id=流式聊天补全>流式聊天补全<a href=#流式聊天补全 class=hash-link aria-label="Direct link to 流式聊天补全" title="Direct link to 流式聊天补全">​</a></h2>
<p>推理模型也支持流式聊天补全。<code>reasoning_content</code> 字段在 <a href=https://platform.openai.com/docs/api-reference/chat/streaming target=_blank rel="noopener noreferrer">聊天补全响应块</a> 的 <code>delta</code> 字段中可用。</p>
<div class="language-plain codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-plain codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token plain">{</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    "id": "chatcmpl-123",</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    "object": "chat.completion.chunk",</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    "created": 1694268190,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    "model": "deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B",</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    "system_fingerprint": "fp_44709d6fcb",</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    "choices": [</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        {</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            "index": 0,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            "delta": {</span><br></span><span class=token-line style=color:#393A34><span class="token plain">                "role": "assistant",</span><br></span><span class=token-line style=color:#393A34><span class="token plain">                "reasoning_content": "is",</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            },</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            "logprobs": null,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            "finish_reason": null</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        }</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    ]</span><br></span><span class=token-line style=color:#393A34><span class="token plain">}</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<p>OpenAI 的 Python 客户端库官方不支持流式输出中的 <code>reasoning_content</code> 属性。但客户端支持在响应中添加额外的属性。你可以使用 <code>hasattr</code> 来检查响应中是否存在 <code>reasoning_content</code> 属性。例如：</p>
<div class="language-python codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-python codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token keyword" style=color:#00009f>from</span><span class="token plain"> openai </span><span class="token keyword" style=color:#00009f>import</span><span class="token plain"> OpenAI</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token comment" style=color:#999988;font-style:italic># 修改 OpenAI 的 API 密钥和 API 基地址，以便使用 vLLM 的 API 服务器。</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">openai_api_key </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"EMPTY"</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">openai_api_base </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"http://localhost:8000/v1"</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">client </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> OpenAI</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    api_key</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">openai_api_key</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    base_url</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">openai_api_base</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">models </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> client</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">models</span><span class="token punctuation" style=color:#393A34>.</span><span class="token builtin">list</span><span class="token punctuation" style=color:#393A34>(</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">model </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> models</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">data</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token builtin">id</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">messages </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>[</span><span class="token punctuation" style=color:#393A34>{</span><span class="token string" style=color:#e3116c>"role"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"user"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"content"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"9.11 and 9.8, which is greater?"</span><span class="token punctuation" style=color:#393A34>}</span><span class="token punctuation" style=color:#393A34>]</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">stream </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> client</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">chat</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">completions</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">create</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">model</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">model</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">                                        messages</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">messages</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">                                        stream</span><span class="token operator" style=color:#393A34>=</span><span class="token boolean" style=color:#36acaa>True</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string" style=color:#e3116c>"client: Start streaming chat completions..."</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">printed_reasoning_content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>False</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">printed_content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>False</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>for</span><span class="token plain"> chunk </span><span class="token keyword" style=color:#00009f>in</span><span class="token plain"> stream</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    reasoning_content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>None</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>None</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token comment" style=color:#999988;font-style:italic># 检查内容是 reasoning_content 还是 content。</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token keyword" style=color:#00009f>if</span><span class="token plain"> </span><span class="token builtin">hasattr</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">chunk</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">choices</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">delta</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"reasoning_content"</span><span class="token punctuation" style=color:#393A34>)</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        reasoning_content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> chunk</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">choices</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">delta</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">reasoning_content</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token keyword" style=color:#00009f>elif</span><span class="token plain"> </span><span class="token builtin">hasattr</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">chunk</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">choices</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">delta</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"content"</span><span class="token punctuation" style=color:#393A34>)</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> chunk</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">choices</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">delta</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">content</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token keyword" style=color:#00009f>if</span><span class="token plain"> reasoning_content </span><span class="token keyword" style=color:#00009f>is</span><span class="token plain"> </span><span class="token keyword" style=color:#00009f>not</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>None</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token keyword" style=color:#00009f>if</span><span class="token plain"> </span><span class="token keyword" style=color:#00009f>not</span><span class="token plain"> printed_reasoning_content</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            printed_reasoning_content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>True</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            </span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string" style=color:#e3116c>"reasoning_content:"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> end</span><span class="token operator" style=color:#393A34>=</span><span class="token string" style=color:#e3116c>""</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> flush</span><span class="token operator" style=color:#393A34>=</span><span class="token boolean" style=color:#36acaa>True</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">reasoning_content</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> end</span><span class="token operator" style=color:#393A34>=</span><span class="token string" style=color:#e3116c>""</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> flush</span><span class="token operator" style=color:#393A34>=</span><span class="token boolean" style=color:#36acaa>True</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token keyword" style=color:#00009f>elif</span><span class="token plain"> content </span><span class="token keyword" style=color:#00009f>is</span><span class="token plain"> </span><span class="token keyword" style=color:#00009f>not</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>None</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token keyword" style=color:#00009f>if</span><span class="token plain"> </span><span class="token keyword" style=color:#00009f>not</span><span class="token plain"> printed_content</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            printed_content </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token boolean" style=color:#36acaa>True</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            </span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string" style=color:#e3116c>"\ncontent:"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> end</span><span class="token operator" style=color:#393A34>=</span><span class="token string" style=color:#e3116c>""</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> flush</span><span class="token operator" style=color:#393A34>=</span><span class="token boolean" style=color:#36acaa>True</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token comment" style=color:#999988;font-style:italic># 提取并打印内容</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">content</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> end</span><span class="token operator" style=color:#393A34>=</span><span class="token string" style=color:#e3116c>""</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> flush</span><span class="token operator" style=color:#393A34>=</span><span class="token boolean" style=color:#36acaa>True</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<p>请记住在访问响应之前检查响应中是否存在 <code>reasoning_content</code>。您可以查看<a href=https://github.com/vllm-project/vllm/blob/main/examples/online_serving/openai_chat_completion_with_reasoning_streaming.py target=_blank rel="noopener noreferrer">示例 </a>。</p>
<h2 class="anchor anchorWithStickyNavbar_Ntt5" id=结构化输出>结构化输出<a href=#结构化输出 class=hash-link aria-label="Direct link to 结构化输出" title="Direct link to 结构化输出">​</a></h2>
<p>推理内容也可在结构化输出中找到。像 <code>xgrammar</code> 这样的结构化输出引擎将使用推理内容来生成结构化输出。</p>
<div class="language-python codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-python codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token keyword" style=color:#00009f>from</span><span class="token plain"> openai </span><span class="token keyword" style=color:#00009f>import</span><span class="token plain"> OpenAI</span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>from</span><span class="token plain"> pydantic </span><span class="token keyword" style=color:#00009f>import</span><span class="token plain"> BaseModel</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token comment" style=color:#999988;font-style:italic># 修改 OpenAI 的 API 密钥和 API 基地址，以便使用 vLLM 的 API 服务器。</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">openai_api_key </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"EMPTY"</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">openai_api_base </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"http://localhost:8000/v1"</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">client </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> OpenAI</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    api_key</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">openai_api_key</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    base_url</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">openai_api_base</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">models </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> client</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">models</span><span class="token punctuation" style=color:#393A34>.</span><span class="token builtin">list</span><span class="token punctuation" style=color:#393A34>(</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">model </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> models</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">data</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token builtin">id</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>class</span><span class="token plain"> </span><span class="token class-name">People</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">BaseModel</span><span class="token punctuation" style=color:#393A34>)</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    name</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token builtin">str</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    age</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token builtin">int</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">json_schema </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> People</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">model_json_schema</span><span class="token punctuation" style=color:#393A34>(</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">prompt </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>(</span><span class="token string" style=color:#e3116c>"Generate a JSON with the name and age of one random person."</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">completion </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> client</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">chat</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">completions</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">create</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    model</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">model</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    messages</span><span class="token operator" style=color:#393A34>=</span><span class="token punctuation" style=color:#393A34>[</span><span class="token punctuation" style=color:#393A34>{</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token string" style=color:#e3116c>"role"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"user"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token string" style=color:#e3116c>"content"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> prompt</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token punctuation" style=color:#393A34>}</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    extra_body</span><span class="token operator" style=color:#393A34>=</span><span class="token punctuation" style=color:#393A34>{</span><span class="token string" style=color:#e3116c>"guided_json"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> json_schema</span><span class="token punctuation" style=color:#393A34>}</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string" style=color:#e3116c>"reasoning_content: "</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> completion</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">choices</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">message</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">reasoning_content</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string" style=color:#e3116c>"content: "</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> completion</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">choices</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">message</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">content</span><span class="token punctuation" style=color:#393A34>)</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_Ntt5" id=工具调用>工具调用<a href=#工具调用 class=hash-link aria-label="Direct link to 工具调用" title="Direct link to 工具调用">​</a></h2>
<p>当工具调用和推理解析器都处于启用状态时，推理内容也可用。此外，工具调用仅分析 <code>content</code> 字段中的函数，而不分析 <code>reasoning_content</code> 中的函数。</p>
<div class="language-python codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-python codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token keyword" style=color:#00009f>from</span><span class="token plain"> openai </span><span class="token keyword" style=color:#00009f>import</span><span class="token plain"> OpenAI</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">client </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> OpenAI</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">base_url</span><span class="token operator" style=color:#393A34>=</span><span class="token string" style=color:#e3116c>"http://localhost:8000/v1"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> api_key</span><span class="token operator" style=color:#393A34>=</span><span class="token string" style=color:#e3116c>"dummy"</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">tools </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>[</span><span class="token punctuation" style=color:#393A34>{</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token string" style=color:#e3116c>"type"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"function"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token string" style=color:#e3116c>"function"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>{</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token string" style=color:#e3116c>"name"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"get_weather"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token string" style=color:#e3116c>"description"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"Get the current weather in a given location"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token string" style=color:#e3116c>"parameters"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>{</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            </span><span class="token string" style=color:#e3116c>"type"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"object"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            </span><span class="token string" style=color:#e3116c>"properties"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>{</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">                </span><span class="token string" style=color:#e3116c>"location"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>{</span><span class="token string" style=color:#e3116c>"type"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"string"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"description"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"City and state, e.g., 'San Francisco, CA'"</span><span class="token punctuation" style=color:#393A34>}</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">                </span><span class="token string" style=color:#e3116c>"unit"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>{</span><span class="token string" style=color:#e3116c>"type"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"string"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"enum"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>[</span><span class="token string" style=color:#e3116c>"celsius"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"fahrenheit"</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>}</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            </span><span class="token punctuation" style=color:#393A34>}</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            </span><span class="token string" style=color:#e3116c>"required"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token punctuation" style=color:#393A34>[</span><span class="token string" style=color:#e3116c>"location"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"unit"</span><span class="token punctuation" style=color:#393A34>]</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token punctuation" style=color:#393A34>}</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token punctuation" style=color:#393A34>}</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token punctuation" style=color:#393A34>}</span><span class="token punctuation" style=color:#393A34>]</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">response </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> client</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">chat</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">completions</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">create</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    model</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">client</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">models</span><span class="token punctuation" style=color:#393A34>.</span><span class="token builtin">list</span><span class="token punctuation" style=color:#393A34>(</span><span class="token punctuation" style=color:#393A34>)</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">data</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token builtin">id</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    messages</span><span class="token operator" style=color:#393A34>=</span><span class="token punctuation" style=color:#393A34>[</span><span class="token punctuation" style=color:#393A34>{</span><span class="token string" style=color:#e3116c>"role"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"user"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"content"</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"What's the weather like in San Francisco?"</span><span class="token punctuation" style=color:#393A34>}</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    tools</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">tools</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    tool_choice</span><span class="token operator" style=color:#393A34>=</span><span class="token string" style=color:#e3116c>"auto"</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">response</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">tool_call </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> response</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">choices</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">message</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">tool_calls</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">function</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string-interpolation string" style=color:#e3116c>f"reasoning_content: </span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>{</span><span class="token string-interpolation interpolation">response</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>.</span><span class="token string-interpolation interpolation">choices</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>[</span><span class="token string-interpolation interpolation number" style=color:#36acaa>0</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>]</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>.</span><span class="token string-interpolation interpolation">message</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>.</span><span class="token string-interpolation interpolation">reasoning_content</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>}</span><span class="token string-interpolation string" style=color:#e3116c>"</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string-interpolation string" style=color:#e3116c>f"Function called: </span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>{</span><span class="token string-interpolation interpolation">tool_call</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>.</span><span class="token string-interpolation interpolation">name</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>}</span><span class="token string-interpolation string" style=color:#e3116c>"</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>print</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string-interpolation string" style=color:#e3116c>f"Arguments: </span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>{</span><span class="token string-interpolation interpolation">tool_call</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>.</span><span class="token string-interpolation interpolation">arguments</span><span class="token string-interpolation interpolation punctuation" style=color:#393A34>}</span><span class="token string-interpolation string" style=color:#e3116c>"</span><span class="token punctuation" style=color:#393A34>)</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_Ntt5" id=如何支持新的推理模型>如何支持新的推理模型<a href=#如何支持新的推理模型 class=hash-link aria-label="Direct link to 如何支持新的推理模型" title="Direct link to 如何支持新的推理模型">​</a></h2>
<p>您可以添加一个新的 <code>ReasoningParser</code>，类似于 <code>vllm/entrypoints/openai/reasoning_parsers/deepseek_r1_reasoning_parser.py</code>。</p>
<div class="language-plain codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-plain codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token plain"># 导入所需的包</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">from vllm.entrypoints.openai.reasoning_parsers.abs_reasoning_parsers import (</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    ReasoningParser, ReasoningParserManager)</span><br></span><span class=token-line style=color:#393A34><span class="token plain">from vllm.entrypoints.openai.protocol import (ChatCompletionRequest,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">                                              DeltaMessage)</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain"># 定义一个推理解析器并将其注册到 vLLM</span><br></span><span class=token-line style=color:#393A34><span class="token plain"># register_module 中的名称列表可以在</span><br></span><span class=token-line style=color:#393A34><span class="token plain"># --reasoning-parser 中使用。</span><br></span><span class=token-line style=color:#393A34><span class="token plain">@ReasoningParserManager.register_module(["example"])</span><br></span><span class=token-line style=color:#393A34><span class="token plain">class ExampleParser(ReasoningParser):</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    def __init__(self, tokenizer: AnyTokenizer):</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        super().__init__(tokenizer)</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    def extract_reasoning_content_streaming(</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        self,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        previous_text: str,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        current_text: str,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        delta_text: str,</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        previous_token_ids: Sequence[int],</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        current_token_ids: Sequence[int],</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        delta_token_ids: Sequence[int],</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    ) -> Union[DeltaMessage, None]:</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        """</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        Instance method that should be implemented for extracting reasoning</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        from an incomplete response; for use when handling reasoning calls and</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        streaming. Has to be an instance method because  it requires state -</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        the current tokens/diffs, but also the information about what has</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        previously been parsed and extracted (see constructor)</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        实例方法，用于从未完成的响应中提取推理内容；</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        适用于处理推理调用和流式传输时。</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        必须是一个实例方法，因为它需要状态 -</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        当前的 token/差异，以及之前解析和提取的信息（参见构造函数）。</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        """</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    def extract_reasoning_content(</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            self, model_output: str, request: ChatCompletionRequest</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    ) -> Tuple[Optional[str], Optional[str]]:</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        """</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        从完整的模型生成字符串中提取推理内容。</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        用于非流式响应，其中我们在发送给客户端之前拥有完整的模型响应。</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        参数：</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        model_output: str</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            要从中提取推理内容的模型生成字符串。</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        request: ChatCompletionRequest</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            用于生成 model_output 的请求对象。</span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        返回：</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        Tuple[Optional[str], Optional[str]]</span><br></span><span class=token-line style=color:#393A34><span class="token plain">            包含推理内容和内容的元组。</span><br></span><span class=token-line style=color:#393A34><span class="token plain">        """</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<p>此外，要启用结构化输出，您需要创建一个类似于 中的新 <code>Reasoner</code> <code>vllm/model_executor/guided_decoding/reasoner/deepseek_reasoner.py</code> 。</p>
<div class="language-python codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-python codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token decorator annotation punctuation" style=color:#393A34>@dataclass</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain"></span><span class="token keyword" style=color:#00009f>class</span><span class="token plain"> </span><span class="token class-name">DeepSeekReasoner</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">Reasoner</span><span class="token punctuation" style=color:#393A34>)</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token triple-quoted-string string" style=color:#e3116c>"""</span><br></span><span class=token-line style=color:#393A34><span class="token triple-quoted-string string" style=color:#e3116c>    DeepSeek R 系列模型的推理器。</span><br></span><span class=token-line style=color:#393A34><span class="token triple-quoted-string string" style=color:#e3116c>    """</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    start_token_id</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token builtin">int</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    end_token_id</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token builtin">int</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    start_token</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token builtin">str</span><span class="token plain"> </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"&lt;think>"</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    end_token</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token builtin">str</span><span class="token plain"> </span><span class="token operator" style=color:#393A34>=</span><span class="token plain"> </span><span class="token string" style=color:#e3116c>"&lt;/think>"</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    @</span><span class="token builtin">classmethod</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token keyword" style=color:#00009f>def</span><span class="token plain"> </span><span class="token function" style=color:#d73a49>from_tokenizer</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">cls</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> tokenizer</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> PreTrainedTokenizer</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"> </span><span class="token operator" style=color:#393A34>-</span><span class="token operator" style=color:#393A34>></span><span class="token plain"> Reasoner</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token keyword" style=color:#00009f>return</span><span class="token plain"> cls</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">start_token_id</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">tokenizer</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">encode</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">            </span><span class="token string" style=color:#e3116c>"&lt;think>"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> add_special_tokens</span><span class="token operator" style=color:#393A34>=</span><span class="token boolean" style=color:#36acaa>False</span><span class="token punctuation" style=color:#393A34>)</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">                   end_token_id</span><span class="token operator" style=color:#393A34>=</span><span class="token plain">tokenizer</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">encode</span><span class="token punctuation" style=color:#393A34>(</span><span class="token string" style=color:#e3116c>"&lt;/think>"</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">                                                 add_special_tokens</span><span class="token operator" style=color:#393A34>=</span><span class="token boolean" style=color:#36acaa>False</span><span class="token punctuation" style=color:#393A34>)</span><span class="token punctuation" style=color:#393A34>[</span><span class="token number" style=color:#36acaa>0</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain" style=display:inline-block></span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token keyword" style=color:#00009f>def</span><span class="token plain"> </span><span class="token function" style=color:#d73a49>is_reasoning_end</span><span class="token punctuation" style=color:#393A34>(</span><span class="token plain">self</span><span class="token punctuation" style=color:#393A34>,</span><span class="token plain"> input_ids</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"> </span><span class="token builtin">list</span><span class="token punctuation" style=color:#393A34>[</span><span class="token builtin">int</span><span class="token punctuation" style=color:#393A34>]</span><span class="token punctuation" style=color:#393A34>)</span><span class="token plain"> </span><span class="token operator" style=color:#393A34>-</span><span class="token operator" style=color:#393A34>></span><span class="token plain"> </span><span class="token builtin">bool</span><span class="token punctuation" style=color:#393A34>:</span><span class="token plain"></span><br></span><span class=token-line style=color:#393A34><span class="token plain">        </span><span class="token keyword" style=color:#00009f>return</span><span class="token plain"> self</span><span class="token punctuation" style=color:#393A34>.</span><span class="token plain">end_token_id </span><span class="token keyword" style=color:#00009f>in</span><span class="token plain"> input_ids</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    </span><span class="token punctuation" style=color:#393A34>.</span><span class="token punctuation" style=color:#393A34>.</span><span class="token punctuation" style=color:#393A34>.</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<p>像 <code>xgrammar</code> 这样的结构化输出引擎将使用 <code>end_token_id</code> 来检查模型输出中是否存在推理内容，如果是，则跳过结构化输出。</p>
<p>最后，您可以使用 <code>--enable-reasoning</code> 和 <code>--reasoning-parser</code> 标志为模型启用推理。</p>
<div class="language-plain codeBlockContainer_SigF theme-code-block" style=--prism-color:#393A34;--prism-background-color:#f6f8fa><div class=codeBlockContent_Vp9Z><pre tabindex=0 class="prism-code language-plain codeBlock_oTkS thin-scrollbar" style=color:#393A34;background-color:#f6f8fa><code class=codeBlockLines_QKIK><span class=token-line style=color:#393A34><span class="token plain">vllm serve &lt;model_tag> \</span><br></span><span class=token-line style=color:#393A34><span class="token plain">    --enable-reasoning --reasoning-parser example</span><br></span></code></pre><div class=buttonGroup_xAid><button type=button aria-label="Copy code to clipboard" title=Copy class=clean-btn><span class=copyButtonIcons_JcPr aria-hidden=true><svg viewBox="0 0 24 24" class=copyButtonIcon_AydN><path fill=currentColor d="M19,21H8V7H19M19,5H8A2,2 0 0,0 6,7V21A2,2 0 0,0 8,23H19A2,2 0 0,0 21,21V7A2,2 0 0,0 19,5M16,1H4A2,2 0 0,0 2,3V17H4V3H16V1Z"/></svg><svg viewBox="0 0 24 24" class=copyButtonSuccessIcon_t8Z_><path fill=currentColor d=M21,7L9,19L3.5,13.5L4.91,12.09L9,16.17L19.59,5.59L21,7Z /></svg></span></button></div></div></div>
<h2 class="anchor anchorWithStickyNavbar_Ntt5" id=局限性>局限性<a href=#局限性 class=hash-link aria-label="Direct link to 局限性" title="Direct link to 局限性">​</a></h2>
<ul>
<li>推理内容仅适用于在线服务的聊天补全端点（<code>/v1/chat/completions</code>）。</li>
</ul></div><footer class="theme-doc-footer docusaurus-mt-lg"><div class="row margin-top--sm theme-doc-footer-edit-meta-row"><div class=col><a href=https://github.com/hyperai/vllm-cn/tree/master/docs/03-features/04-reasoning_outputs.md target=_blank rel="noopener noreferrer" class=theme-edit-this-page><svg fill=currentColor height=20 width=20 viewBox="0 0 40 40" class=iconEdit_Cj19 aria-hidden=true><g><path d="m34.5 11.7l-3 3.1-6.3-6.3 3.1-3q0.5-0.5 1.2-0.5t1.1 0.5l3.9 3.9q0.5 0.4 0.5 1.1t-0.5 1.2z m-29.5 17.1l18.4-18.5 6.3 6.3-18.4 18.4h-6.3v-6.2z"/></g></svg>Edit this page</a></div><div class="col lastUpdated_yfew"></div></div></footer></article><nav class="pagination-nav docusaurus-mt-lg" aria-label="Docs pages"><a class="pagination-nav__link pagination-nav__link--prev" href=/docs/features/tool_calling><div class=pagination-nav__sublabel>Previous</div><div class=pagination-nav__label>工具调用</div></a><a class="pagination-nav__link pagination-nav__link--next" href=/docs/features/structured_outputs><div class=pagination-nav__sublabel>Next</div><div class=pagination-nav__label>结构化输出</div></a></nav></div></div><div class="col col--3"><div class="tableOfContents__yE1 thin-scrollbar theme-doc-toc-desktop"><ul class="table-of-contents table-of-contents__left-border"><li><a href=#支持的模型 class="table-of-contents__link toc-highlight">支持的模型</a><li><a href=#快速入门 class="table-of-contents__link toc-highlight">快速入门</a><li><a href=#流式聊天补全 class="table-of-contents__link toc-highlight">流式聊天补全</a><li><a href=#结构化输出 class="table-of-contents__link toc-highlight">结构化输出</a><li><a href=#工具调用 class="table-of-contents__link toc-highlight">工具调用</a><li><a href=#如何支持新的推理模型 class="table-of-contents__link toc-highlight">如何支持新的推理模型</a><li><a href=#局限性 class="table-of-contents__link toc-highlight">局限性</a></ul></div></div></div></div></main></div></div></div><footer class=footer><div class="container container-fluid"><div class="footer__bottom text--center"><div class=footer__copyright>© 2025 Hyper.AI for Chinese Simplified mirror</div></div></div></footer></div>