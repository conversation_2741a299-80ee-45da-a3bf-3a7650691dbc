<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试HTML文档</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        h1 { color: #333; border-bottom: 2px solid #333; }
        h2 { color: #666; border-bottom: 1px solid #666; }
        h3 { color: #999; }
        .highlight { background-color: yellow; }
        .code { background-color: #f4f4f4; padding: 10px; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>HTML转Markdown测试文档</h1>
    
    <p>这是一个用于测试HTML转Markdown功能的示例文档。</p>
    
    <h2>基本文本格式</h2>
    
    <p>这是一个普通段落，包含<strong>粗体文本</strong>和<em>斜体文本</em>。</p>
    
    <p>这里有一个<span class="highlight">高亮文本</span>的示例。</p>
    
    <h3>列表示例</h3>
    
    <h4>无序列表</h4>
    <ul>
        <li>第一个列表项</li>
        <li>第二个列表项
            <ul>
                <li>嵌套列表项1</li>
                <li>嵌套列表项2</li>
            </ul>
        </li>
        <li>第三个列表项</li>
    </ul>
    
    <h4>有序列表</h4>
    <ol>
        <li>步骤一：准备工作</li>
        <li>步骤二：执行操作</li>
        <li>步骤三：验证结果</li>
    </ol>
    
    <h2>代码和引用</h2>
    
    <h3>内联代码</h3>
    <p>使用<code>console.log()</code>来输出调试信息。</p>
    
    <h3>代码块</h3>
    <div class="code">
        <pre>
function greet(name) {
    console.log("Hello, " + name + "!");
}

greet("World");
        </pre>
    </div>
    
    <h3>引用</h3>
    <blockquote>
        <p>这是一个引用块的示例。引用通常用于突出显示重要的文本或引用他人的话。</p>
        <footer>— 引用来源</footer>
    </blockquote>
    
    <h2>链接和图片</h2>
    
    <h3>链接</h3>
    <p>访问<a href="https://www.example.com">示例网站</a>获取更多信息。</p>
    
    <h3>图片</h3>
    <p>这里应该有一张图片：</p>
    <img src="https://via.placeholder.com/300x200" alt="示例图片" />
    
    <h2>表格</h2>
    
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <thead>
            <tr>
                <th>姓名</th>
                <th>年龄</th>
                <th>职业</th>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td>张三</td>
                <td>25</td>
                <td>工程师</td>
            </tr>
            <tr>
                <td>李四</td>
                <td>30</td>
                <td>设计师</td>
            </tr>
            <tr>
                <td>王五</td>
                <td>28</td>
                <td>产品经理</td>
            </tr>
        </tbody>
    </table>
    
    <h2>特殊内容</h2>
    
    <h3>分割线</h3>
    <p>下面是一条分割线：</p>
    <hr>
    
    <h3>换行和段落</h3>
    <p>这是第一行。<br>
    这是第二行，使用了换行标签。</p>
    
    <p>这是一个新的段落。</p>
    
    <h2>嵌套结构测试</h2>
    
    <h3>复杂嵌套</h3>
    <div>
        <h4>子标题</h4>
        <p>这是一个嵌套在div中的段落。</p>
        
        <h5>更深层的标题</h5>
        <p>这里测试更深层次的标题结构。</p>
        
        <h6>最深层标题</h6>
        <p>这是H6级别的标题内容。</p>
    </div>
    
    <h2>总结</h2>
    
    <p>这个HTML文档包含了各种常见的HTML元素，用于测试HTML到Markdown的转换功能。转换后应该能够保持文档的结构和格式。</p>
    
    <p><strong>注意：</strong>某些CSS样式可能在转换过程中丢失，这是正常现象。</p>
    
</body>
</html>
