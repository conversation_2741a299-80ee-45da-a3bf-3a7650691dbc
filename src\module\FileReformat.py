"""
文件格式转换模块
负责PDF、HTML转Markdown的格式转换功能
"""

import time
import logging
import logging.handlers
import asyncio
from pathlib import Path
from typing import Dict, Any
import pypandoc
from src.utils.DataProcess import DataProcess
from src.module.pdf_converter import PDFConverter

# 配置日志（同时输出到控制台和文件）
log_dir = Path("logs")
log_dir.mkdir(exist_ok=True)

logger = logging.getLogger(__name__)
if not logger.handlers:  # 避免重复添加处理器
    logger.setLevel(logging.INFO)

    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)

    # 文件处理器
    file_handler = logging.handlers.TimedRotatingFileHandler(
        log_dir / "file_reformat.log",
        when='midnight',
        interval=1,
        backupCount=30,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)

    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    logger.propagate = False


class FileReformat:
    """文件格式转换器"""

    def __init__(self):
        self.data_processor = DataProcess()
        self.base_receive_dir = Path("tmp_file_receive")
        self.base_markdown_dir = Path("tmp_file_markdown")

        # 并发控制：限制同时处理的任务数量
        self.pdf_semaphore = asyncio.Semaphore(2)  # 最多2个PDF同时处理
        self.html_semaphore = asyncio.Semaphore(4)  # 最多4个HTML同时处理

        # 初始化PDF转换器
        self.pdf_converter = PDFConverter(self.pdf_semaphore)
    
    async def convert_to_markdown(self, metadata: Dict[str, Any]) -> str:
        """
        主要功能：根据文件类型转换为Markdown
        
        Args:
            metadata: 文件元数据字典
            
        Returns:
            转换后的markdown文件路径
        """
        file_type = metadata.get("file_type", "").lower()
        
        if file_type == "markdown":
            return await self._copy_markdown_file(metadata)
        elif file_type == "pdf":
            return await self._convert_pdf_to_markdown(metadata)
        elif file_type in ["html", "htm"]:
            return await self._convert_html_to_markdown(metadata)
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
    
    async def _copy_markdown_file(self, metadata: Dict[str, Any]) -> str:
        """复制Markdown文件到markdown目录"""
        try:
            # 源文件路径
            src_file = self.base_receive_dir / metadata["document_id"] / metadata["tmp_file_name"]
            
            # 目标目录
            target_dir = self.base_markdown_dir / metadata["document_id"]
            target_dir.mkdir(parents=True, exist_ok=True)
            
            # 目标文件路径
            target_file = target_dir / f"{metadata['snow_id']}_markdown.md"
            
            # 读取源文件内容
            content = await self.data_processor.read_file(str(src_file))
            
            # 写入目标文件
            await self.data_processor.write_file_normal(str(target_file), content)
            
            return str(target_file)
            
        except Exception as e:
            raise Exception(f"Markdown文件复制失败: {e}")
    
    async def _convert_pdf_to_markdown(self, metadata: Dict[str, Any]) -> str:
        """使用PDF转换器将PDF转换为Markdown"""
        try:
            # 源文件路径
            src_file = self.base_receive_dir / metadata["document_id"] / metadata["tmp_file_name"]

            # 目标目录
            target_dir = self.base_markdown_dir / metadata["document_id"]
            target_dir.mkdir(parents=True, exist_ok=True)

            # 目标文件路径
            target_file = target_dir / f"{metadata['snow_id']}_markdown.md"

            logger.info(f"[PDF_CONVERT_START] 开始PDF转换 - src: {src_file}, target: {target_file}")

            try:
                # 使用PDF转换器进行转换（自动选择转换方式）
                start_time = time.time()
                markdown_content = await self.pdf_converter.convert_pdf_to_markdown(src_file, target_dir)
                duration = time.time() - start_time

                # 写入目标文件
                with open(target_file, 'w', encoding='utf-8') as f:
                    f.write(markdown_content)

                logger.info(f"[PDF_CONVERT_SUCCESS] PDF转换成功 - duration: {duration:.2f}s, output: {target_file}")
                return str(target_file)

            except Exception as e:
                logger.error(f"[PDF_CONVERT_ERROR] PDF转换失败: {e}")
                return await self._convert_pdf_fallback(metadata)

        except Exception as e:
            raise Exception(f"PDF转换失败: {e}")
    
    async def _convert_pdf_fallback(self, metadata: Dict[str, Any]) -> str:
        """PDF转换备用方案（使用pypandoc或其他工具）"""
        try:
            logger.warning(f"[PDF_FALLBACK] 使用PDF转换备用方案 - document_id: {metadata['document_id']}")

            # 源文件路径
            src_file = self.base_receive_dir / metadata["document_id"] / metadata["tmp_file_name"]

            # 目标目录
            target_dir = self.base_markdown_dir / metadata["document_id"]
            target_dir.mkdir(parents=True, exist_ok=True)

            # 目标文件路径
            target_file = target_dir / f"{metadata['snow_id']}_markdown.md"

            # 使用pypandoc作为备用方案（需要安装pandoc）
            try:
                logger.info(f"[PDF_FALLBACK_PYPANDOC] 尝试使用pypandoc转换PDF")
                start_time = time.time()
                output = pypandoc.convert_file(
                    str(src_file),
                    'markdown',
                    format='pdf'
                )
                duration = time.time() - start_time

                # 写入转换结果
                await self.data_processor.write_file_normal(str(target_file), output.encode('utf-8'))
                logger.info(f"[PDF_FALLBACK_SUCCESS] pypandoc转换成功 - duration: {duration:.2f}s, output: {target_file}")
                return str(target_file)

            except Exception as e:
                logger.error(f"[PDF_FALLBACK_PYPANDOC_ERROR] pypandoc转换失败: {e}")
                # 如果pypandoc也失败，创建一个错误提示文件
                error_content = f"""# PDF转换失败

原文件: {metadata['raw_file_name']}
错误: 无法转换PDF文件，请检查MinerU或pandoc是否正确安装

## 解决方案
1. 安装MinerU: `pip install magic-pdf`
2. 或安装pandoc: https://pandoc.org/installing.html
3. 手动转换PDF为Markdown格式后重新上传
"""
                await self.data_processor.write_file_normal(str(target_file), error_content.encode('utf-8'))
                logger.info(f"[PDF_FALLBACK_ERROR_FILE] 创建错误提示文件 - output: {target_file}")
                return str(target_file)

        except Exception as e:
            logger.error(f"[PDF_FALLBACK_ERROR] PDF备用转换失败: {e}")
            raise Exception(f"PDF备用转换失败: {e}")
    
    async def _convert_html_to_markdown(self, metadata: Dict[str, Any]) -> str:
        """使用pandoc将HTML转换为Markdown"""
        try:
            # 源文件路径
            src_file = self.base_receive_dir / metadata["document_id"] / metadata["tmp_file_name"]

            # 目标目录
            target_dir = self.base_markdown_dir / metadata["document_id"]
            target_dir.mkdir(parents=True, exist_ok=True)

            # 目标文件路径
            target_file = target_dir / f"{metadata['snow_id']}_markdown.md"

            logger.info(f"[HTML_CONVERT_START] 开始HTML转换 - src: {src_file}, target: {target_file}")

            try:
                # 首先尝试使用pandoc命令行工具
                cmd = [
                    "pandoc",
                    str(src_file.resolve()),  # 使用绝对路径
                    "-f", "html",
                    "-t", "markdown",
                    "-o", str(target_file.resolve()),  # 使用绝对路径
                    "--wrap=none"
                ]

                logger.info(f"[PANDOC_CMD] 执行Pandoc命令: {' '.join(cmd)}")

                # 使用信号量控制并发，但在线程池中执行同步subprocess
                start_time = time.time()

                # 使用信号量控制并发
                async with self.html_semaphore:
                    logger.info(f"[PANDOC_CONCURRENT] 获取HTML处理信号量，开始处理")

                    # 在线程池中执行同步subprocess，避免阻塞事件循环
                    import concurrent.futures
                    import subprocess

                    def run_pandoc_sync():
                        """在线程池中同步执行Pandoc"""
                        process = subprocess.Popen(
                            cmd,
                            stdout=subprocess.PIPE,
                            stderr=subprocess.STDOUT,
                            text=True,
                            bufsize=1,
                            universal_newlines=True
                        )

                        stdout_lines = []
                        while True:
                            output = process.stdout.readline()
                            if output == '' and process.poll() is not None:
                                break
                            if output:
                                output = output.strip()
                                stdout_lines.append(output)

                        return_code = process.poll()
                        return return_code, stdout_lines

                    # 在线程池中异步执行
                    loop = asyncio.get_event_loop()
                    with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                        return_code, stdout_lines = await loop.run_in_executor(
                            executor, run_pandoc_sync
                        )

                    duration = time.time() - start_time

                    # 记录输出
                    for output in stdout_lines:
                        if output:  # 只记录非空输出
                            logger.info(f"[PANDOC_OUTPUT] {output}")

                    logger.info(f"[PANDOC_RESULT] Pandoc执行完成 - duration: {duration:.2f}s, return_code: {return_code}")
                    logger.info(f"[PANDOC_CONCURRENT] 释放HTML处理信号量")

                    # 记录完整输出到文件
                    if stdout_lines:
                        full_output = '\n'.join(stdout_lines)
                        logger.debug(f"[PANDOC_FULL_OUTPUT] 完整输出:\n{full_output}")

                if return_code == 0 and target_file.exists():
                    logger.info(f"[HTML_CONVERT_SUCCESS] Pandoc转换成功 - output: {target_file}")
                    return str(target_file)
                else:
                    logger.warning(f"[PANDOC_FALLBACK] Pandoc命令行失败，尝试pypandoc")
                    # 尝试使用pypandoc
                    start_time = time.time()
                    output = pypandoc.convert_file(
                        str(src_file),
                        'markdown',
                        format='html',
                        extra_args=['--wrap=none']
                    )
                    duration = time.time() - start_time

                    # 写入转换结果
                    await self.data_processor.write_file_normal(str(target_file), output.encode('utf-8'))
                    logger.info(f"[HTML_CONVERT_SUCCESS] pypandoc转换成功 - duration: {duration:.2f}s, output: {target_file}")
                    return str(target_file)

            except FileNotFoundError:
                logger.warning(f"[PANDOC_NOT_FOUND] pandoc命令未找到，尝试pypandoc")
                # pandoc命令不存在，使用pypandoc
                start_time = time.time()
                output = pypandoc.convert_file(
                    str(src_file),
                    'markdown',
                    format='html',
                    extra_args=['--wrap=none']
                )
                duration = time.time() - start_time

                # 写入转换结果
                await self.data_processor.write_file_normal(str(target_file), output.encode('utf-8'))
                logger.info(f"[HTML_CONVERT_SUCCESS] pypandoc转换成功 - duration: {duration:.2f}s, output: {target_file}")
                return str(target_file)

            except Exception as e:
                logger.error(f"[HTML_CONVERT_ERROR] Pandoc转换失败: {e}，尝试简单HTML解析")
                import traceback
                logger.error(f"[HTML_CONVERT_ERROR_DETAIL] Pandoc异常详情: {traceback.format_exc()}")
                # 如果pandoc失败，尝试简单的HTML解析
                return await self._convert_html_simple(metadata)

        except Exception as e:
            raise Exception(f"HTML转换失败: {e}")
    
    async def _convert_html_simple(self, metadata: Dict[str, Any]) -> str:
        """简单的HTML转Markdown转换（备用方案）"""
        try:
            logger.warning(f"[HTML_FALLBACK] 使用HTML转换备用方案 - document_id: {metadata['document_id']}")

            from html2text import html2text

            # 源文件路径
            src_file = self.base_receive_dir / metadata["document_id"] / metadata["tmp_file_name"]

            # 目标目录
            target_dir = self.base_markdown_dir / metadata["document_id"]
            target_dir.mkdir(parents=True, exist_ok=True)

            # 目标文件路径
            target_file = target_dir / f"{metadata['snow_id']}_markdown.md"

            # 读取HTML内容
            start_time = time.time()
            html_content = await self.data_processor.read_file(str(src_file))
            html_text = html_content.decode('utf-8', errors='ignore')

            # 转换为Markdown
            markdown_content = html2text(html_text)

            # 写入转换结果
            await self.data_processor.write_file_normal(str(target_file), markdown_content.encode('utf-8'))
            duration = time.time() - start_time

            logger.info(f"[HTML_FALLBACK_SUCCESS] html2text转换成功 - duration: {duration:.2f}s, output: {target_file}")
            return str(target_file)

        except ImportError:
            logger.error(f"[HTML_FALLBACK_IMPORT_ERROR] html2text未安装")
            # 如果html2text未安装，创建错误提示文件
            target_dir = self.base_markdown_dir / metadata["document_id"]
            target_dir.mkdir(parents=True, exist_ok=True)
            target_file = target_dir / f"{metadata['snow_id']}_markdown.md"

            error_content = f"""# HTML转换失败

原文件: {metadata['raw_file_name']}
错误: 无法转换HTML文件，请安装必要的依赖

## 解决方案
1. 安装pandoc: https://pandoc.org/installing.html
2. 或安装html2text: `pip install html2text`
3. 手动转换HTML为Markdown格式后重新上传
"""
            await self.data_processor.write_file_normal(str(target_file), error_content.encode('utf-8'))
            logger.info(f"[HTML_FALLBACK_ERROR_FILE] 创建错误提示文件 - output: {target_file}")
            return str(target_file)

        except Exception as e:
            logger.error(f"[HTML_FALLBACK_ERROR] HTML简单转换失败: {e}")
            raise Exception(f"HTML简单转换失败: {e}")
    
    async def get_markdown_content(self, metadata: Dict[str, Any]) -> str:
        """获取转换后的Markdown内容"""
        markdown_file = self.base_markdown_dir / metadata["document_id"] / f"{metadata['snow_id']}_markdown.md"
        
        if not markdown_file.exists():
            raise FileNotFoundError(f"Markdown文件不存在: {markdown_file}")
        
        content = await self.data_processor.read_file(str(markdown_file))
        return content.decode('utf-8', errors='ignore')
