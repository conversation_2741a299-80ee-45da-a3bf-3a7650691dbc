"""
数据处理工具模块
负责文件操作、雪花ID生成、文件上传下载等核心功能
"""

import os
import time
import shutil
import aiofiles
import requests
from typing import Dict, Any, Optional
from pathlib import Path
from fastapi import UploadFile

# 使用文件扩展名进行类型检测（不依赖magic库）


class SnowflakeIDGenerator:
    """雪花ID生成器"""
    
    def __init__(self, machine_id: int = 1, datacenter_id: int = 1):
        self.machine_id = machine_id
        self.datacenter_id = datacenter_id
        self.sequence = 0
        self.last_timestamp = -1
        
        # 各部分位数
        self.machine_id_bits = 5
        self.datacenter_id_bits = 5
        self.sequence_bits = 12
        
        # 最大值
        self.max_machine_id = -1 ^ (-1 << self.machine_id_bits)
        self.max_datacenter_id = -1 ^ (-1 << self.datacenter_id_bits)
        self.sequence_mask = -1 ^ (-1 << self.sequence_bits)
        
        # 位移
        self.machine_id_shift = self.sequence_bits
        self.datacenter_id_shift = self.sequence_bits + self.machine_id_bits
        self.timestamp_left_shift = self.sequence_bits + self.machine_id_bits + self.datacenter_id_bits
        
        # 起始时间戳 (2020-01-01)
        self.twepoch = 1577836800000
        
        if machine_id > self.max_machine_id or machine_id < 0:
            raise ValueError(f"机器ID必须在0到{self.max_machine_id}之间")
        if datacenter_id > self.max_datacenter_id or datacenter_id < 0:
            raise ValueError(f"数据中心ID必须在0到{self.max_datacenter_id}之间")
    
    def _current_millis(self) -> int:
        """获取当前毫秒时间戳"""
        return int(time.time() * 1000)
    
    def _til_next_millis(self, last_timestamp: int) -> int:
        """等待下一毫秒"""
        timestamp = self._current_millis()
        while timestamp <= last_timestamp:
            timestamp = self._current_millis()
        return timestamp
    
    def next_id(self) -> int:
        """生成下一个雪花ID"""
        timestamp = self._current_millis()
        
        if timestamp < self.last_timestamp:
            raise Exception("时钟回拨，拒绝生成ID")
        
        if self.last_timestamp == timestamp:
            self.sequence = (self.sequence + 1) & self.sequence_mask
            if self.sequence == 0:
                timestamp = self._til_next_millis(self.last_timestamp)
        else:
            self.sequence = 0
        
        self.last_timestamp = timestamp
        
        return ((timestamp - self.twepoch) << self.timestamp_left_shift) | \
               (self.datacenter_id << self.datacenter_id_shift) | \
               (self.machine_id << self.machine_id_shift) | \
               self.sequence


class DataProcess:
    """数据处理工具类"""
    
    def __init__(self):
        self.snowflake = SnowflakeIDGenerator()
        self.base_receive_dir = Path("tmp_file_receive")
        self.base_markdown_dir = Path("tmp_file_markdown")
        
        # 确保目录存在
        self.base_receive_dir.mkdir(exist_ok=True)
        self.base_markdown_dir.mkdir(exist_ok=True)
    
    def _get_file_type(self, file_path: str) -> str:
        """获取文件类型（基于文件扩展名）"""
        ext = Path(file_path).suffix.lower()

        if ext == '.pdf':
            return 'pdf'
        elif ext in ['.htm', '.html']:
            return 'html'
        elif ext in ['.md', '.markdown']:
            return 'markdown'
        else:
            # 对于未知扩展名，尝试读取文件内容的前几行来判断
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    first_lines = f.read(1024).lower()
                    if first_lines.startswith('<!doctype html') or '<html' in first_lines:
                        return 'html'
                    elif first_lines.startswith('#') or '##' in first_lines:
                        return 'markdown'
            except Exception:
                pass

            return 'unknown'
    
    def _create_file_metadata(self, raw_file_name: str, file_path: str, document_id: str) -> Dict[str, Any]:
        """创建文件元数据字典"""
        snow_id = str(self.snowflake.next_id())
        file_type = self._get_file_type(file_path)
        file_ext = Path(raw_file_name).suffix
        tmp_file_name = f"{snow_id}{file_ext}"
        timestamp = str(int(time.time()))
        
        return {
            "raw_file_name": raw_file_name,
            "file_type": file_type,
            "snow_id": snow_id,
            "tmp_file_name": tmp_file_name,
            "timestamp": timestamp,
            "document_id": document_id
        }
    
    async def write_file_tmp(self, file_content: bytes, raw_file_name: str, document_id: str) -> Dict[str, Any]:
        """文件tmp写入方法"""
        # 创建文档目录
        doc_dir = self.base_receive_dir / document_id
        doc_dir.mkdir(parents=True, exist_ok=True)
        
        # 创建临时文件用于类型检测
        temp_path = doc_dir / raw_file_name
        async with aiofiles.open(temp_path, 'wb') as f:
            await f.write(file_content)
        
        # 创建文件元数据
        metadata = self._create_file_metadata(raw_file_name, str(temp_path), document_id)
        
        # 重命名为雪花ID文件名
        final_path = doc_dir / metadata["tmp_file_name"]
        temp_path.rename(final_path)
        
        return metadata
    
    async def write_file_normal(self, file_path: str, content: bytes) -> bool:
        """文件普通写入方法"""
        try:
            file_path_obj = Path(file_path)
            file_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiofiles.open(file_path, 'wb') as f:
                await f.write(content)
            return True
        except Exception as e:
            print(f"文件写入失败: {e}")
            return False
    
    async def copy_file(self, src_path: str, document_id: str) -> Dict[str, Any]:
        """文件复制方法"""
        src_path_obj = Path(src_path)
        if not src_path_obj.exists():
            raise FileNotFoundError(f"源文件不存在: {src_path}")
        
        # 读取文件内容
        async with aiofiles.open(src_path, 'rb') as f:
            content = await f.read()
        
        # 调用tmp写入方法
        return await self.write_file_tmp(content, src_path_obj.name, document_id)
    
    async def read_file(self, file_path: str) -> bytes:
        """文件读取方法"""
        async with aiofiles.open(file_path, 'rb') as f:
            return await f.read()
    
    async def download_file(self, file_url: str, document_id: str) -> Dict[str, Any]:
        """文件下载方法"""
        try:
            response = requests.get(file_url, stream=True)
            response.raise_for_status()
            
            # 从URL获取文件名
            file_name = Path(file_url).name
            if not file_name or '.' not in file_name:
                # 如果无法从URL获取文件名，使用Content-Disposition
                content_disposition = response.headers.get('content-disposition')
                if content_disposition:
                    import re
                    filename_match = re.search(r'filename="?([^"]+)"?', content_disposition)
                    if filename_match:
                        file_name = filename_match.group(1)
                    else:
                        file_name = f"download_{int(time.time())}.bin"
                else:
                    file_name = f"download_{int(time.time())}.bin"
            
            # 下载文件内容
            content = response.content
            
            # 调用tmp写入方法
            return await self.write_file_tmp(content, file_name, document_id)
            
        except Exception as e:
            raise Exception(f"文件下载失败: {e}")
    
    async def parse_formdata_file(self, upload_file: UploadFile, document_id: str) -> Dict[str, Any]:
        """解析formdata文件方法"""
        try:
            # 确保文件名存在
            filename = upload_file.filename or "unknown_file"

            # 读取上传文件内容
            content = await upload_file.read()

            # 重置文件指针（如果需要的话）
            if hasattr(upload_file, 'seek'):
                try:
                    await upload_file.seek(0)
                except:
                    pass  # 忽略seek错误

            # 调用tmp写入方法
            return await self.write_file_tmp(content, filename, document_id)

        except Exception as e:
            raise Exception(f"FormData文件解析失败: {e}")
    
    async def copy_to_markdown_dir(self, metadata: Dict[str, Any], content: bytes) -> str:
        """将文件复制到markdown目录"""
        markdown_dir = self.base_markdown_dir / metadata["document_id"]
        markdown_dir.mkdir(exist_ok=True)
        
        markdown_filename = f"{metadata['snow_id']}_markdown.md"
        markdown_path = markdown_dir / markdown_filename
        
        async with aiofiles.open(markdown_path, 'wb') as f:
            await f.write(content)
        
        return str(markdown_path)
