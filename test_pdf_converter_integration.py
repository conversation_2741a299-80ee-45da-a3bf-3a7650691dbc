#!/usr/bin/env python3
"""
测试PDF转换器整合功能
"""

import asyncio
import logging
from pathlib import Path
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.module.pdf_converter import PDFConverter
from src.module.FileReformat import FileReformat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_pdf_converter_direct():
    """直接测试PDF转换器"""
    logger.info("=== 测试PDF转换器直接调用 ===")
    
    # 创建测试PDF文件路径（这里需要一个实际的PDF文件）
    test_pdf = Path("test_sample.pdf")
    
    if not test_pdf.exists():
        logger.warning(f"测试PDF文件不存在: {test_pdf}")
        logger.info("请创建一个测试PDF文件或修改路径")
        return False
    
    try:
        # 创建PDF转换器实例
        converter = PDFConverter()
        
        # 测试TOC检测
        has_toc = converter.has_toc(test_pdf)
        logger.info(f"PDF是否有TOC: {has_toc}")
        
        # 测试转换
        output_dir = Path("test_output")
        output_dir.mkdir(exist_ok=True)
        
        markdown_content = await converter.convert_pdf_to_markdown(test_pdf, output_dir)
        
        # 保存结果
        result_file = output_dir / "direct_test_result.md"
        with open(result_file, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        
        logger.info(f"直接测试成功，结果保存到: {result_file}")
        logger.info(f"转换内容长度: {len(markdown_content)} 字符")
        return True
        
    except Exception as e:
        logger.error(f"直接测试失败: {e}")
        return False


async def test_file_reformat_integration():
    """测试FileReformat集成"""
    logger.info("=== 测试FileReformat集成 ===")
    
    # 创建测试目录结构
    test_receive_dir = Path("tmp_file_receive")
    test_markdown_dir = Path("tmp_file_markdown")
    
    test_receive_dir.mkdir(exist_ok=True)
    test_markdown_dir.mkdir(exist_ok=True)
    
    # 创建测试文档目录
    doc_id = "test_doc_001"
    doc_dir = test_receive_dir / doc_id
    doc_dir.mkdir(exist_ok=True)
    
    # 假设有一个测试PDF文件
    test_pdf = Path("test_sample.pdf")
    if not test_pdf.exists():
        logger.warning(f"测试PDF文件不存在: {test_pdf}")
        logger.info("请创建一个测试PDF文件或修改路径")
        return False
    
    # 复制测试文件到测试目录
    import shutil
    test_file_name = "test.pdf"
    target_file = doc_dir / test_file_name
    shutil.copy2(test_pdf, target_file)
    
    try:
        # 创建FileReformat实例
        file_reformat = FileReformat()
        
        # 创建测试元数据
        metadata = {
            "document_id": doc_id,
            "tmp_file_name": test_file_name,
            "snow_id": "SNOW001",
            "raw_file_name": "test_sample.pdf",
            "file_type": "pdf"
        }
        
        # 测试转换
        result_path = await file_reformat.convert_to_markdown(metadata)
        
        logger.info(f"FileReformat集成测试成功，结果文件: {result_path}")
        
        # 检查结果文件
        if Path(result_path).exists():
            with open(result_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"转换内容长度: {len(content)} 字符")
            logger.info(f"内容预览: {content[:200]}...")
        
        return True
        
    except Exception as e:
        logger.error(f"FileReformat集成测试失败: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False
    
    finally:
        # 清理测试文件
        if target_file.exists():
            target_file.unlink()


async def test_toc_detection():
    """测试TOC检测功能"""
    logger.info("=== 测试TOC检测功能 ===")
    
    test_pdf = Path("test_sample.pdf")
    if not test_pdf.exists():
        logger.warning(f"测试PDF文件不存在: {test_pdf}")
        return False
    
    try:
        converter = PDFConverter()
        has_toc = converter.has_toc(test_pdf)
        
        logger.info(f"TOC检测结果: {has_toc}")
        
        if has_toc:
            logger.info("PDF有TOC，将使用原生方法转换")
            try:
                content = converter.convert_with_native_method(test_pdf)
                logger.info(f"原生方法转换成功，内容长度: {len(content)}")
            except Exception as e:
                logger.error(f"原生方法转换失败: {e}")
        else:
            logger.info("PDF无TOC，将使用MinerU方法转换")
            # 注意：这里不实际测试MinerU，因为需要安装和配置
            logger.info("MinerU方法测试跳过（需要实际环境）")
        
        return True
        
    except Exception as e:
        logger.error(f"TOC检测测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    logger.info("开始PDF转换器整合测试")
    
    # 测试列表
    tests = [
        ("TOC检测功能", test_toc_detection),
        ("PDF转换器直接调用", test_pdf_converter_direct),
        ("FileReformat集成", test_file_reformat_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"开始测试: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = await test_func()
            results.append((test_name, result))
            logger.info(f"测试 '{test_name}' {'成功' if result else '失败'}")
        except Exception as e:
            logger.error(f"测试 '{test_name}' 异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    logger.info(f"\n{'='*50}")
    logger.info("测试总结")
    logger.info(f"{'='*50}")
    
    for test_name, result in results:
        status = "✓ 成功" if result else "✗ 失败"
        logger.info(f"{test_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    logger.info(f"\n总计: {success_count}/{total_count} 个测试通过")
    
    return success_count == total_count


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
