# 日志LLM数据加载器

一个支持PDF、HTML、Markdown文件解析和文档树生成的数据接入服务。

## 功能特性

- 🚀 **多格式支持**: 支持PDF、HTML、HTM、Markdown文件格式
- 🔄 **智能转换**: 自动将各种格式转换为Markdown
- 🌳 **文档树生成**: 根据标题层级自动生成目录树结构
- 📡 **RESTful API**: 提供完整的HTTP接口服务
- ⚡ **高并发**: 支持异步处理和后台任务
- 🧪 **完整测试**: 包含单元测试和接口测试

## 技术架构

### 核心组件

```mermaid
graph TD
    A[文件上传/下载读取] --> J[文件缓存]
    J --> O[FileReformat]
    O --> G{判断文件类型}
    G --> H[markdown]
    H --> M
    G --> I[PDF]
    G --> L[html,htm]
    I --> C[minerU转换]
    C --> M[结果markdown]
    L --> N[pandoc转换]
    N --> M
    M --> E[Markdown2DocumentTree]
    E --> F[生成基础文档块缓存目录]
```

### 技术栈

- **语言**: Python 3.11
- **包管理**: uv
- **Web框架**: FastAPI
- **文档转换**: MinerU (PDF), Pandoc (HTML)
- **测试框架**: pytest
- **异步支持**: asyncio, aiofiles

## 快速开始

### 环境要求

- Python 3.11+
- Windows环境
- CUDA环境（可选，用于GPU加速）

### 安装

1. 克隆项目
```bash
git clone <repository-url>
cd log_llm_data_loader
```

2. 安装依赖
```bash
uv sync
```

3. 启动服务
```bash
python main.py
```

服务将在 `http://localhost:8000` 启动。

### API文档

启动服务后，访问以下地址查看API文档：
- Swagger UI: `http://localhost:8000/docs`
- ReDoc: `http://localhost:8000/redoc`

## API接口

### 文件上传

#### 1. 通过URL上传
```http
POST /upload/url
Content-Type: application/json

{
    "file_url": "http://example.com/document.pdf",
    "document_id": "doc_001"
}
```

#### 2. 通过本地路径上传
```http
POST /upload/local
Content-Type: application/json

{
    "file_url": "/path/to/local/document.pdf",
    "document_id": "doc_002"
}
```

#### 3. 通过FormData上传
```http
POST /upload/formdata
Content-Type: multipart/form-data

file: <文件>
document_id: doc_003
```

### 状态查询

```http
GET /status/{document_id}
```

### 文档树获取

```http
GET /tree/{document_id}
```

### 文档内容获取

```http
GET /content/{document_id}/{file_path}
```

### 健康检查

```http
GET /health
```

## 项目结构

```
log_llm_data_loader/
├── src/
│   ├── utils/
│   │   ├── __init__.py
│   │   └── DataProcess.py          # 数据处理工具
│   ├── module/
│   │   ├── __init__.py
│   │   ├── FileReformat.py         # 文件格式转换
│   │   └── Markdown2DocumentTree.py # Markdown转文档树
│   ├── service/
│   │   ├── __init__.py
│   │   └── logllm_data_access_service.py # 主服务
│   └── test/
│       ├── __init__.py
│       ├── test_data_process.py    # DataProcess测试
│       ├── test_file_reformat.py   # FileReformat测试
│       ├── test_markdown2documenttree.py # Markdown2DocumentTree测试
│       ├── test_api.py             # API接口测试
│       ├── sample_test.md          # 测试用例文件
│       └── run_tests.py            # 测试运行脚本
├── tmp_file_receive/               # 接收文件临时目录
├── tmp_file_markdown/              # Markdown文件目录
├── tmp_document_tree/              # 文档树目录
├── main.py                         # 应用入口
├── pyproject.toml                  # 项目配置
└── README.md                       # 项目说明
```

## 核心模块说明

### DataProcess (utils/DataProcess.py)
负责文件操作和数据处理：
- 雪花ID生成
- 文件上传下载
- 文件类型检测
- 临时文件管理

### FileReformat (module/FileReformat.py)
负责文件格式转换：
- PDF转Markdown（使用MinerU）
- HTML转Markdown（使用Pandoc）
- Markdown文件复制

### Markdown2DocumentTree (module/Markdown2DocumentTree.py)
负责文档树生成：
- Markdown解析
- 标题层级分析
- 目录结构生成
- 文档块切分

### LogLLMDataAccessService (service/logllm_data_access_service.py)
主要业务服务：
- RESTful API接口
- 业务流程调度
- 异步任务处理

## 使用示例

### 上传并处理Markdown文件

```python
import requests

# 上传文件
response = requests.post(
    "http://localhost:8000/upload/local",
    json={
        "file_url": "/path/to/document.md",
        "document_id": "test_doc_001"
    }
)

# 检查状态
status = requests.get("http://localhost:8000/status/test_doc_001")
print(status.json())

# 获取文档树
tree = requests.get("http://localhost:8000/tree/test_doc_001")
print(tree.json())
```

### 生成的文档树结构示例

对于包含以下内容的Markdown文件：
```markdown
## vllm
### vllm使用
vllm我们会这么使用AAAA
##### 问题与内容
也许会遇到问题BBB
### vllm开发
## vllm的部署
部署代表了CCC
### vllm的结构
包含了ABCD
```

将生成如下目录结构：
```
├─vllm
│  └─vllm使用
│      │  123456789_1.txt  # vllm我们会这么使用AAAA
│      │
│      └─问题与内容
│              123456789_2.txt  # 也许会遇到问题BBB
│
└─vllm的部署
    │  123456789_3.txt  # 部署代表了CCC
    │
    └─vllm的结构
            123456789_4.txt  # 包含了ABCD
```

## 测试

### 运行所有测试
```bash
python src/test/run_tests.py
```

### 运行特定测试
```bash
# 单元测试
pytest src/test/test_data_process.py -v

# API测试
pytest src/test/test_api.py -v

# 所有测试
pytest src/test/ -v
```

### 测试覆盖率
```bash
pytest src/test/ --cov=src --cov-report=html
```

## 开发指南

### 代码规范
- 使用Python 3.11语法特性
- 遵循PEP 8代码风格
- 所有注释和文档使用中文
- 使用类型提示

### 添加新功能
1. 在相应模块中实现功能
2. 编写单元测试
3. 更新API文档
4. 运行完整测试套件

### 依赖管理
使用uv进行包管理：
```bash
# 添加依赖
uv add package_name

# 添加开发依赖
uv add --dev package_name

# 更新依赖
uv sync
```

## 部署

### 生产环境部署
```bash
# 使用uvicorn部署
uvicorn main:app --host 0.0.0.0 --port 8000 --workers 4

# 或使用gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker
```

### Docker部署
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install uv
RUN uv sync

EXPOSE 8000
CMD ["python", "main.py"]
```

## 故障排除

### 常见问题

1. **MinerU安装失败**
   - 确保CUDA环境正确安装
   - 检查GPU驱动版本

2. **Pandoc转换失败**
   - 安装Pandoc: https://pandoc.org/installing.html
   - 检查PATH环境变量

3. **文件权限错误**
   - 确保临时目录有写权限
   - 检查文件路径是否正确

4. **内存不足**
   - 调整并发处理数量
   - 增加系统内存

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 许可证

[添加许可证信息]

## 更新日志

### v1.0.0 (2024-09-02)
- 初始版本发布
- 支持PDF、HTML、Markdown文件处理
- 实现文档树生成功能
- 提供完整的RESTful API
- 包含完整的测试套件