# PDF转换器整合说明

## 概述

本次整合将原本在 `FileReformat._convert_pdf_to_markdown` 中的MinerU转换逻辑迁移到了专业的 `pdf_converter.py` 模块中，实现了智能的PDF转换分流机制。

## 整合内容

### 1. 增强的 `pdf_converter.py` 模块

新增了 `PDFConverter` 类，提供以下功能：

#### 主要方法：

- **`has_toc(pdf_file: Path) -> bool`**
  - 检查PDF是否包含目录信息（TOC）
  - 用于决定使用哪种转换方法

- **`convert_with_native_method(pdf_file: Path) -> str`**
  - 使用原生方法（pymupdf4llm）转换PDF
  - 适用于有TOC的PDF文件
  - 基于目录结构进行内容分块

- **`convert_with_mineru_method(pdf_file: Path, output_dir: Path) -> str`**
  - 使用MinerU方法转换PDF
  - 适用于无TOC的PDF文件
  - 支持异步执行和并发控制

- **`convert_pdf_to_markdown(pdf_file: Path, output_dir: Path) -> str`**
  - 智能转换方法，自动选择最适合的转换方式
  - 有TOC：优先使用原生方法，失败时回退到MinerU
  - 无TOC：直接使用MinerU方法

### 2. 简化的 `FileReformat._convert_pdf_to_markdown` 方法

原本复杂的MinerU调用逻辑被简化为：

```python
async def _convert_pdf_to_markdown(self, metadata: Dict[str, Any]) -> str:
    """使用PDF转换器将PDF转换为Markdown"""
    # 获取文件路径
    src_file = self.base_receive_dir / metadata["document_id"] / metadata["tmp_file_name"]
    target_dir = self.base_markdown_dir / metadata["document_id"]
    target_file = target_dir / f"{metadata['snow_id']}_markdown.md"
    
    # 使用PDF转换器进行转换（自动选择转换方式）
    markdown_content = await self.pdf_converter.convert_pdf_to_markdown(src_file, target_dir)
    
    # 写入目标文件
    with open(target_file, 'w', encoding='utf-8') as f:
        f.write(markdown_content)
    
    return str(target_file)
```

## 转换流程

```mermaid
graph TD
    A[PDF文件] --> B{检查TOC}
    B -->|有TOC| C[使用原生方法]
    B -->|无TOC| D[使用MinerU方法]
    C --> E{转换成功?}
    E -->|是| F[返回结果]
    E -->|否| D
    D --> G{转换成功?}
    G -->|是| F
    G -->|否| H[使用备用方案pypandoc]
```

## 使用方式

### 1. 直接使用PDF转换器

```python
from src.module.pdf_converter import PDFConverter

# 创建转换器实例
converter = PDFConverter()

# 智能转换（推荐）
markdown_content = await converter.convert_pdf_to_markdown(pdf_file, output_dir)

# 手动选择方法
if converter.has_toc(pdf_file):
    content = converter.convert_with_native_method(pdf_file)
else:
    content = await converter.convert_with_mineru_method(pdf_file, output_dir)
```

### 2. 通过FileReformat使用

```python
from src.module.FileReformat import FileReformat

file_reformat = FileReformat()
result_path = await file_reformat.convert_to_markdown(metadata)
```

## 优势

### 1. 智能分流
- 自动检测PDF特征，选择最适合的转换方法
- 有TOC的PDF使用原生方法，转换质量更高
- 无TOC的PDF使用MinerU，兼容性更好

### 2. 代码组织
- 专业的PDF转换工具类，职责单一
- FileReformat专注于文件格式转换流程
- 代码更易维护和扩展

### 3. 错误处理
- 多层级的错误处理和回退机制
- 原生方法失败时自动回退到MinerU
- MinerU失败时回退到pypandoc

### 4. 性能优化
- 保持原有的并发控制机制
- 异步执行，不阻塞主线程
- 智能选择，避免不必要的重试

## 兼容性

- 保持了原有的API接口不变
- 向后兼容现有的调用方式
- 新增的功能不影响现有代码

## 依赖要求

- **原生方法**: `pymupdf4llm`, `pymupdf`
- **MinerU方法**: `mineru` (通过uv运行)
- **备用方案**: `pypandoc`, `pandoc`

## 测试

运行测试脚本验证功能：

```bash
python test_pdf_converter_integration.py
```

运行使用示例：

```bash
python example_usage.py
```

## 注意事项

1. **TOC检测**: 基于pymupdf的get_toc()方法，某些PDF可能检测不准确
2. **MinerU环境**: 需要正确安装和配置MinerU环境
3. **文件路径**: 确保临时目录有足够的读写权限
4. **并发控制**: 通过信号量控制PDF转换的并发数量

## 后续扩展

1. 可以添加更多的PDF转换方法
2. 可以根据PDF大小、页数等特征进一步优化分流逻辑
3. 可以添加转换质量评估机制
4. 可以支持更多的输出格式
