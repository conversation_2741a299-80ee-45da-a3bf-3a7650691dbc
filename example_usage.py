#!/usr/bin/env python3
"""
PDF转换器整合功能使用示例
"""

import asyncio
import logging
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from src.module.pdf_converter import PDFConverter
from src.module.FileReformat import FileReformat

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def example_direct_pdf_converter():
    """示例：直接使用PDF转换器"""
    logger.info("=== 示例：直接使用PDF转换器 ===")
    
    # 创建PDF转换器实例
    converter = PDFConverter()
    
    # 假设有一个PDF文件
    pdf_file = Path("example.pdf")
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    if pdf_file.exists():
        try:
            # 检查PDF是否有TOC
            has_toc = converter.has_toc(pdf_file)
            logger.info(f"PDF文件 {pdf_file} 是否有TOC: {has_toc}")
            
            # 智能转换（自动选择方法）
            markdown_content = await converter.convert_pdf_to_markdown(pdf_file, output_dir)
            
            # 保存结果
            output_file = output_dir / f"{pdf_file.stem}.md"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"转换成功！输出文件: {output_file}")
            logger.info(f"内容长度: {len(markdown_content)} 字符")
            
        except Exception as e:
            logger.error(f"转换失败: {e}")
    else:
        logger.warning(f"PDF文件不存在: {pdf_file}")


async def example_file_reformat():
    """示例：通过FileReformat使用PDF转换器"""
    logger.info("=== 示例：通过FileReformat使用PDF转换器 ===")
    
    # 创建FileReformat实例
    file_reformat = FileReformat()
    
    # 模拟文件元数据（通常来自文件上传系统）
    metadata = {
        "document_id": "doc_123",
        "tmp_file_name": "uploaded_file.pdf",
        "snow_id": "SNOW_456",
        "raw_file_name": "原始文档.pdf",
        "file_type": "pdf"
    }
    
    # 确保目录结构存在
    receive_dir = Path("tmp_file_receive") / metadata["document_id"]
    receive_dir.mkdir(parents=True, exist_ok=True)
    
    # 假设PDF文件已经上传到指定位置
    pdf_file = receive_dir / metadata["tmp_file_name"]
    
    if pdf_file.exists():
        try:
            # 转换PDF为Markdown
            result_path = await file_reformat.convert_to_markdown(metadata)
            logger.info(f"转换成功！结果文件: {result_path}")
            
            # 读取并显示部分内容
            with open(result_path, 'r', encoding='utf-8') as f:
                content = f.read()
            logger.info(f"转换内容长度: {len(content)} 字符")
            logger.info(f"内容预览:\n{content[:300]}...")
            
        except Exception as e:
            logger.error(f"转换失败: {e}")
    else:
        logger.warning(f"PDF文件不存在: {pdf_file}")
        logger.info("请将PDF文件放置到正确位置或修改路径")


async def example_manual_method_selection():
    """示例：手动选择转换方法"""
    logger.info("=== 示例：手动选择转换方法 ===")
    
    converter = PDFConverter()
    pdf_file = Path("example.pdf")
    output_dir = Path("output")
    output_dir.mkdir(exist_ok=True)
    
    if pdf_file.exists():
        try:
            # 检查TOC
            has_toc = converter.has_toc(pdf_file)
            
            if has_toc:
                logger.info("PDF有TOC，使用原生方法转换")
                try:
                    markdown_content = converter.convert_with_native_method(pdf_file)
                    logger.info("原生方法转换成功")
                except Exception as e:
                    logger.error(f"原生方法失败: {e}")
                    logger.info("尝试使用MinerU方法")
                    markdown_content = await converter.convert_with_mineru_method(pdf_file, output_dir)
                    logger.info("MinerU方法转换成功")
            else:
                logger.info("PDF无TOC，使用MinerU方法转换")
                markdown_content = await converter.convert_with_mineru_method(pdf_file, output_dir)
                logger.info("MinerU方法转换成功")
            
            # 保存结果
            output_file = output_dir / f"{pdf_file.stem}_manual.md"
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            logger.info(f"手动选择方法转换成功！输出文件: {output_file}")
            
        except Exception as e:
            logger.error(f"手动选择方法转换失败: {e}")
    else:
        logger.warning(f"PDF文件不存在: {pdf_file}")


async def main():
    """主函数"""
    logger.info("PDF转换器整合功能使用示例")
    
    # 运行各种示例
    examples = [
        ("直接使用PDF转换器", example_direct_pdf_converter),
        ("通过FileReformat使用", example_file_reformat),
        ("手动选择转换方法", example_manual_method_selection),
    ]
    
    for example_name, example_func in examples:
        logger.info(f"\n{'='*60}")
        logger.info(f"运行示例: {example_name}")
        logger.info(f"{'='*60}")
        
        try:
            await example_func()
        except Exception as e:
            logger.error(f"示例 '{example_name}' 运行失败: {e}")
    
    logger.info(f"\n{'='*60}")
    logger.info("所有示例运行完成")
    logger.info(f"{'='*60}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())
