"""
完整的端到端测试脚本
测试四种上传方式：FormData上传PDF、FormData上传HTML、本地路径PDF、本地路径HTML
"""

import asyncio
import time
import requests
import json
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S'
)
logger = logging.getLogger(__name__)


class EndToEndTester:
    """端到端测试器"""
    
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
        self.test_results = []
        
        # 测试文件路径
        self.test_files = {
            "pdf1": "src/test/云池数通设备配置助手PRD1.0.pdf",
            "pdf2": "src/test/NE40E V800R023C10SPC500 配置指南 基础配置.pdf", 
            "html1": "src/test/sample_test.html",
            "html2": "src/test/推理输出 _ vLLM 中文站.html"
        }
    
    def log_result(self, test_name: str, success: bool, message: str = "", details: dict = None):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} {test_name}: {message}")
        
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": time.time()
        }
        if details:
            result["details"] = details
            
        self.test_results.append(result)
    
    def test_health_check(self):
        """测试健康检查"""
        try:
            logger.info("[TEST_START] 健康检查测试")
            response = requests.get(f"{self.base_url}/health", timeout=10)
            if response.status_code == 200:
                self.log_result("健康检查", True, "服务正常运行")
                return True
            else:
                self.log_result("健康检查", False, f"状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_result("健康检查", False, f"连接失败: {e}")
            return False
    
    def test_formdata_upload_pdf(self):
        """测试FormData上传PDF"""
        try:
            logger.info("[TEST_START] FormData上传PDF测试")
            test_file = self.test_files["pdf1"]
            document_id = "formdata_pdf_test"
            
            if not Path(test_file).exists():
                self.log_result("FormData PDF文件检查", False, f"文件不存在: {test_file}")
                return False
            
            # 上传文件
            with open(test_file, 'rb') as f:
                files = {'file': (Path(test_file).name, f, 'application/pdf')}
                data = {'document_id': document_id}
                
                response = requests.post(
                    f"{self.base_url}/upload/formdata",
                    files=files,
                    data=data,
                    timeout=30
                )
            
            if response.status_code != 200:
                self.log_result("FormData PDF上传", False, f"上传失败: {response.status_code}")
                return False
            
            self.log_result("FormData PDF上传", True, "文件上传成功")
            
            # 等待处理完成
            return self._wait_for_processing(document_id, "FormData PDF", max_wait=3000)
            
        except Exception as e:
            self.log_result("FormData PDF测试", False, f"异常: {e}")
            return False
    
    def test_formdata_upload_html(self):
        """测试FormData上传HTML"""
        try:
            logger.info("[TEST_START] FormData上传HTML测试")
            test_file = self.test_files["html1"]
            document_id = "formdata_html_test"
            
            if not Path(test_file).exists():
                self.log_result("FormData HTML文件检查", False, f"文件不存在: {test_file}")
                return False
            
            # 上传文件
            with open(test_file, 'rb') as f:
                files = {'file': (Path(test_file).name, f, 'text/html')}
                data = {'document_id': document_id}
                
                response = requests.post(
                    f"{self.base_url}/upload/formdata",
                    files=files,
                    data=data,
                    timeout=30
                )
            
            if response.status_code != 200:
                self.log_result("FormData HTML上传", False, f"上传失败: {response.status_code}")
                return False
            
            self.log_result("FormData HTML上传", True, "文件上传成功")
            
            # 等待处理完成
            return self._wait_for_processing(document_id, "FormData HTML", max_wait=60)
            
        except Exception as e:
            self.log_result("FormData HTML测试", False, f"异常: {e}")
            return False
    
    def test_local_path_pdf(self):
        """测试本地路径PDF"""
        try:
            logger.info("[TEST_START] 本地路径PDF测试")
            test_file = self.test_files["pdf2"]
            document_id = "local_pdf_test"
            
            if not Path(test_file).exists():
                self.log_result("本地路径PDF文件检查", False, f"文件不存在: {test_file}")
                return False
            
            # 上传文件
            response = requests.post(
                f"{self.base_url}/upload/local",
                json={
                    "file_url": test_file,
                    "document_id": document_id
                },
                timeout=30
            )
            
            if response.status_code != 200:
                self.log_result("本地路径PDF上传", False, f"上传失败: {response.status_code}")
                return False
            
            self.log_result("本地路径PDF上传", True, "文件上传成功")
            
            # 等待处理完成
            return self._wait_for_processing(document_id, "本地路径PDF", max_wait=3000)
            
        except Exception as e:
            self.log_result("本地路径PDF测试", False, f"异常: {e}")
            return False
    
    def test_local_path_html(self):
        """测试本地路径HTML"""
        try:
            logger.info("[TEST_START] 本地路径HTML测试")
            test_file = self.test_files["html2"]
            document_id = "local_html_test"
            
            if not Path(test_file).exists():
                self.log_result("本地路径HTML文件检查", False, f"文件不存在: {test_file}")
                return False
            
            # 上传文件
            response = requests.post(
                f"{self.base_url}/upload/local",
                json={
                    "file_url": test_file,
                    "document_id": document_id
                },
                timeout=30
            )
            
            if response.status_code != 200:
                self.log_result("本地路径HTML上传", False, f"上传失败: {response.status_code}")
                return False
            
            self.log_result("本地路径HTML上传", True, "文件上传成功")
            
            # 等待处理完成
            return self._wait_for_processing(document_id, "本地路径HTML", max_wait=60)
            
        except Exception as e:
            self.log_result("本地路径HTML测试", False, f"异常: {e}")
            return False
    
    def _wait_for_processing(self, document_id: str, test_type: str, max_wait: int = 3000):
        """等待处理完成"""
        logger.info(f"[WAIT_START] 等待{test_type}处理完成（最多{max_wait}秒）...")
        
        wait_interval = 10
        for elapsed in range(0, max_wait, wait_interval):
            time.sleep(wait_interval)
            
            try:
                # 检查状态
                status_response = requests.get(f"{self.base_url}/status/{document_id}", timeout=10)
                if status_response.status_code == 200:
                    status_data = status_response.json()
                    stages = status_data["data"]["stages"]
                    
                    logger.info(f"[WAIT_PROGRESS] {test_type}处理进度 ({elapsed+wait_interval}s): "
                              f"接收={stages['file_received']}, "
                              f"转换={stages['markdown_converted']}, "
                              f"文档树={stages['document_tree_generated']}")
                    
                    if stages["file_received"]:
                        self.log_result(f"{test_type}文件接收", True, "文件接收成功")
                        
                        if stages["markdown_converted"]:
                            self.log_result(f"{test_type}转Markdown", True, "转换成功")
                            
                            if stages["document_tree_generated"]:
                                self.log_result(f"{test_type}文档树生成", True, "文档树生成成功")
                                
                                # 获取文档树详情
                                tree_response = requests.get(f"{self.base_url}/tree/{document_id}", timeout=10)
                                if tree_response.status_code == 200:
                                    tree_data = tree_response.json()
                                    structure = tree_data["data"]["structure"]
                                    
                                    # 统计文档树信息
                                    def count_nodes(node):
                                        count = 1
                                        if "children" in node:
                                            for child in node["children"]:
                                                count += count_nodes(child)
                                        return count
                                    
                                    total_nodes = count_nodes(structure)
                                    
                                    self.log_result(f"{test_type}文档树获取", True, 
                                                  f"成功获取文档树结构，共{total_nodes}个节点",
                                                  {"structure": structure})
                                    
                                    logger.info(f"[TEST_SUCCESS] {test_type}测试完成 - 总耗时: {elapsed+wait_interval}s")
                                    return True
                                else:
                                    self.log_result(f"{test_type}文档树获取", False, 
                                                  f"获取失败: {tree_response.status_code}")
                                    return False
                            else:
                                continue  # 继续等待文档树生成
                        else:
                            continue  # 继续等待转换完成
                    else:
                        self.log_result(f"{test_type}文件接收", False, "文件接收失败")
                        return False
                else:
                    self.log_result(f"{test_type}状态检查", False, f"状态检查失败: {status_response.status_code}")
                    return False
                    
            except Exception as e:
                logger.error(f"[WAIT_ERROR] 状态检查异常: {e}")
                continue
        
        # 超时
        self.log_result(f"{test_type}处理", False, f"处理超时（{max_wait}秒）")
        return False
    
    def cleanup_test_files(self):
        """清理测试文件"""
        try:
            import shutil
            cleanup_dirs = [
                "tmp_file_receive",
                "tmp_file_markdown", 
                "tmp_document_tree"
            ]
            
            for dir_name in cleanup_dirs:
                dir_path = Path(dir_name)
                if dir_path.exists():
                    shutil.rmtree(dir_path)
            
            self.log_result("清理测试文件", True, "测试文件清理完成")
        except Exception as e:
            self.log_result("清理测试文件", False, f"清理失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        logger.info("=" * 80)
        logger.info("开始完整端到端测试")
        logger.info("=" * 80)
        
        # 清理之前的测试文件
        # self.cleanup_test_files()
        
        # 运行测试
        tests = [
            ("健康检查", self.test_health_check),
            ("FormData上传PDF", self.test_formdata_upload_pdf),
            ("FormData上传HTML", self.test_formdata_upload_html),
            ("本地路径PDF", self.test_local_path_pdf),
            ("本地路径HTML", self.test_local_path_html),
        ]
        
        for test_name, test_func in tests:
            logger.info(f"\n--- {test_name} ---")
            test_func()
        
        # 最终清理
        # self.cleanup_test_files()
        
        # 输出总结
        logger.info("\n" + "=" * 80)
        logger.info("端到端测试结果总结")
        logger.info("=" * 80)
        
        passed = sum(1 for result in self.test_results if result["success"])
        total = len(self.test_results)
        
        logger.info(f"总测试数: {total}")
        logger.info(f"通过数: {passed}")
        logger.info(f"失败数: {total - passed}")
        logger.info(f"通过率: {passed/total*100:.1f}%")
        
        # 输出详细结果
        logger.info("\n详细测试结果:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            logger.info(f"{status} {result['test']}: {result['message']}")
        
        if passed == total:
            logger.info("\n🎉 所有端到端测试通过！系统工作正常！")
            return True
        else:
            logger.info(f"\n⚠️  有 {total - passed} 个测试失败，请检查系统配置")
            return False


def main():
    """主函数"""
    tester = EndToEndTester()
    success = tester.run_all_tests()
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
